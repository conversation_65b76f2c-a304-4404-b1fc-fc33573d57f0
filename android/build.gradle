group 'com.baidu.mapapi.utils'
version '1.0'

rootProject.allprojects {
    repositories {
        mavenCentral()
       maven { url 'https://maven.aliyun.com/repository/central' }
       maven { url 'https://maven.aliyun.com/repository/jcenter' }
       maven { url 'https://maven.aliyun.com/repository/google' }
       maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
       maven { url 'https://maven.aliyun.com/repository/public' }
//        google()
//        jcenter()
    }
}


apply plugin: 'com.android.library'

buildscript {
    repositories {
        mavenCentral()
       maven { url 'https://maven.aliyun.com/repository/central' }
       maven { url 'https://maven.aliyun.com/repository/jcenter' }
       maven { url 'https://maven.aliyun.com/repository/google' }
       maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
       maven { url 'https://maven.aliyun.com/repository/public' }
//        google()
//        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

android {
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 16
    }
    lintOptions {
        disable 'InvalidPackage'
    }

    sourceSets {
        main{
            jniLibs.srcDirs 'jniLibs'
        }
    }

    lintOptions {
        abortOnError false
    }

}

repositories {
    mavenLocal()
}

dependencies {
    implementation fileTree(includes: ['*.jar'], dir: 'libs')
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Map:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Search:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Util:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Location_All:9.1.8'
}
