package com.ybm100.app.crm.api;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.hycustomer.HyBDFollowUpInfoBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateDetailBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateListBean;
import com.ybm100.app.crm.bean.hycustomer.HyPublicDetailBean;
import com.ybm100.app.crm.bean.hycustomer.HyPublicListBean;
import com.ybm100.app.crm.bean.schedule.ContactListBean;
import com.ybm100.app.crm.task.bean.MerchantBean;
import com.ybm100.app.crm.task.bean.PagingApiBean;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;

/**
 * 荷叶健康公私海api接口
 */
public interface HyApiService {
    /**
     * 私海客户列表
     */
    @POST("hyjk/privateSeaList")
    @FormUrlEncoded
    Observable<RequestBaseBean<HyPrivateListBean>> getPrivateListData(@FieldMap HashMap<String, String> map);

    /**
     * 公海列表
     */
    @POST("hyjk/openSeaList")
    @FormUrlEncoded
    Observable<RequestBaseBean<HyPublicListBean>> searchOpenSea(@FieldMap HashMap<String, String> map);


    /**
     * 释放至公海
     */
    @POST("hyjk/releaseCustomer")
    @FormUrlEncoded
    Observable<RequestBaseBean> releaseToBD(@FieldMap HashMap<String, String> map);

    /**
     * 公海认领
     */
    @POST("hyjk/receiveCustomer")
    @FormUrlEncoded
    Observable<RequestBaseBean> receive(@Field("id") String id);

    /**
     * 公海详情
     */
    @POST("hyjk/openSeaCustomerDetail")
    @FormUrlEncoded
    Observable<RequestBaseBean<HyPublicDetailBean>> searchOpenSeaDetail(@Field("id") String id);


    @GET("task/v290/hy/toAddVisit")
    Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(@Query("customerId") String merchantId, @Query("customerType") String customerType);

    /**
     * 分配至BD
     */
    @GET("hyjk/assignCustomer")
    Observable<RequestBaseBean> distributeToBD(@Query("bindUserId") String bindUserId, @Query("id") String id);
    /**
     *
     * 私海客户获取筛选条件
     */
    @POST("customer/private/list/condition")
    Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems();

    /**
     * 荷叶健康私海客户详情
     */
    @GET("hyjk/privateSeaCustomerDetail")
    Observable<RequestBaseBean<HyPrivateDetailBean>> getBasicDetail(@Query("id") String merchantId);

    /**
     * 荷叶健康bd跟进信息 列表
     */
    @GET("hyjk/queryBDRelationRecordInfo")
    Observable<RequestBaseBean<List<HyBDFollowUpInfoBean>>> getBDFollowUpInfoList(@QueryMap HashMap<String, String> hashMap);

    /**
     * 荷叶健康基本信息联系人列表
     */
    @GET("api/contact/getContactList")
    Observable<RequestBaseBean<List<ContactBean>>> getContactList(@Query("poiId") String poiId);
    /**
     * 荷叶健康选择联系人列表
     */
    @GET("api/contact/getContactList")
    Observable<RequestBaseBean<List<ContactListBean>>> getContactListBySelect(@Query("poiId") String poiId);
    /**
     * 添加联系人
     *
     * @param params
     * @return
     */
    @FormUrlEncoded
    @POST("api/contact/addContact")
    Observable<RequestBaseBean<ContactBean>> addContact(@FieldMap Map<String, Object> params);

    /**
     * 修改联系人
     *
     * @param params
     * @return
     */
    @FormUrlEncoded
    @POST("api/contact/updateContact")
    Observable<RequestBaseBean<ContactBean>> updateContact(@FieldMap Map<String, Object> params);
    /**
     * 搜索任务对象列表
     * 2.0.6 以后新增 apiVersion 字段 传递206
     * <p>
     * 290 url 变更
     *
     * @param authType 0自己以及全县组 1仅自己t
     */
    @GET("hyjk/getPrivateCustomerInfo")
    Observable<RequestBaseBean<PagingApiBean<MerchantBean>>> searchTaskObjectsByKey(@Query("apiVersion") String apiVersion,
                                                                                    @Query("keyword") String keyword,
                                                                                    @Query("tag") String tag,
                                                                                    @Query("limit") int limit,
                                                                                    @Query("offset") int offset,
                                                                                    @Query("authType") int authType,
                                                                                    @Query("ifVIP") int ifVIP,
                                                                                    @Query("latitude") double latitude,
                                                                                    @Query("longitude") double longitude);
}
