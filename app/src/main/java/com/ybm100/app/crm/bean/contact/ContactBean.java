package com.ybm100.app.crm.bean.contact;

import com.chad.library.adapter.base.entity.AbstractExpandableItem;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.ybm100.app.crm.widget.contact.SortToken;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @file ContactBean.java
 * @brief 联系人
 * @date 2018/12/24
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class ContactBean extends AbstractExpandableItem<ContactDependBean> implements MultiItemEntity, Serializable, Cloneable {
    public String sortKey;
    /**
     * 显示数据拼音的首字母
     */
    public String sortLetters;
    public SortToken sortToken = new SortToken();

    public String getSortKey() {
        return sortKey;
    }

    public void setSortKey(String sortKey) {
        this.sortKey = sortKey;
    }

    public String getSortLetters() {
        return sortLetters;
    }

    public void setSortLetters(String sortLetters) {
        this.sortLetters = sortLetters;
    }

    public SortToken getSortToken() {
        return sortToken;
    }

    public void setSortToken(SortToken sortToken) {
        this.sortToken = sortToken;
    }

    public String icon;
    public String contactName;
    public String contactAddress;
    //通话时长
    public String timeDistance;
    //上次通话时间
    public String lastCallTime;
    public String contactMobile;
    public String fixedLine;
    public int itemType;
    public int contactSex = -1;//1男 0女
    public String contactBirth;
    //	必填，Boss：老板，LegalPerson：法人，ShopManager：店长，Handler：经理人，Landlady：老板娘，Buyer：采购，Cashier：收银员，ShopAssistant：店员，Other：其他
    public String contactJobName;//中文 英文
    public String contactJob;
    public String poiId;
    public String poiName;


    public String contactTag;

    public ContactDependBean dependBean;
    public String id;
    public String merchantId;
    public String merchantName;
    public int isEffective;

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }

    public int getIsEffective() {
        return isEffective;
    }

    public void setIsEffective(int isEffective) {
        this.isEffective = isEffective;
    }

    public boolean isKp() {
        return isEffective == 1;
    }

    public ContactBean() {
    }

    public ContactBean(String sortKey) {
        this.sortKey = sortKey;
    }

    public String getContactBirth() {
        return contactBirth;
    }

    public void setContactBirth(String contactBirth) {
        this.contactBirth = contactBirth;
    }


    public String getFixedLine() {
        return fixedLine;
    }

    public void setFixedLine(String fixedLine) {
        this.fixedLine = fixedLine;
    }

    public String getContactTag() {
        return contactTag;
    }

    public void setContactTag(String contactTag) {
        this.contactTag = contactTag;
    }

    @Override
    public int getLevel() {
        return 0;
    }

    @Override
    public int getItemType() {
        return itemType;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getTimeDistance() {
        return timeDistance;
    }

    public void setTimeDistance(String timeDistance) {
        this.timeDistance = timeDistance;
    }

    public String getLastCallTime() {
        return lastCallTime;
    }

    public void setLastCallTime(String lastCallTime) {
        this.lastCallTime = lastCallTime;
    }


    public ContactDependBean getDependBean() {
        return dependBean;
    }

    public void setDependBean(ContactDependBean dependBean) {
        this.dependBean = dependBean;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    @Override
    public ContactBean clone() {
        ContactBean obj = null;
        try {
            obj = (ContactBean) super.clone();
      /*      ArrayList<String> strings = new ArrayList<>();
            if (contactTag != null) {
                for (String tag : contactTag) {
                    strings.add(tag);
                }
            }
            obj.setContactTag(strings);*/
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return obj;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContactBean that = (ContactBean) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getContactMobile() {
        return contactMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public int getContactSex() {
        return contactSex;
    }

    public void setContactSex(int contactSex) {
        this.contactSex = contactSex;
    }

    public String getContactJobName() {
        return contactJobName;
    }

    public void setContactJobName(String contactJobName) {
        this.contactJobName = contactJobName;
    }

    public String getContactJob() {
        return contactJob;
    }

    public void setContactJob(String contactJob) {
        this.contactJob = contactJob;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
}
