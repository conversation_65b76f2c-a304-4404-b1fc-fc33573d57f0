package com.ybm100.app.crm.bean.coupon;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/16
 */
public class VoucherBean implements Serializable {

    private VoucherListBean list;
    private TypeCountBean typeCount;

    public class VoucherListBean {
        private String limit;       //页码大小	number	10
        private String offset;      //当前页码	number	1
        private String pageCount;
        private boolean lastPage; //是否最后也
        private String total;       //总记录数
        private List<RowsBean> rows;

        public class RowsBean {
            private long expireDate;                  // 截止使用时间	string	2017-04-03
            private String minMoneyToEnableDesc;        // 使用条件	string	满3000可用
            private String voucherTitle;        //
            private double moneyInVoucher;              // 优惠券金额	string	50.0
            private String orderNo;                     // 使用的订单id	string	YBM908I95948
            private long validDate;                   // 有效期开始时间	string	2017-02-03
            private String voucherDesc;                 // 使用的范围	string	全场商品使用
            private String voucherType;
            private String voucherTypeDesc;                 // 优惠券类型	string	代金券
            private double discount;//	折扣券额	number
            private String discountStr;//	折扣券额	number
            private String discountDesc;//	折扣券文案	string	0.96折（折扣券规则：券类型是通用券，discount不是空）
            private String unit;

            public long getExpireDate() {
                return expireDate;
            }

            public void setExpireDate(long expireDate) {
                this.expireDate = expireDate;
            }

            public String getMinMoneyToEnableDesc() {
                return minMoneyToEnableDesc;
            }

            public void setMinMoneyToEnableDesc(String minMoneyToEnableDesc) {
                this.minMoneyToEnableDesc = minMoneyToEnableDesc;
            }

            public double getMoneyInVoucher() {
                return moneyInVoucher;
            }

            public void setMoneyInVoucher(double moneyInVoucher) {
                this.moneyInVoucher = moneyInVoucher;
            }

            public String getOrderId() {
                return orderNo;
            }

            public void setOrderId(String orderId) {
                this.orderNo = orderId;
            }

            public long getValidDate() {
                return validDate;
            }

            public void setValidDate(long validDate) {
                this.validDate = validDate;
            }

            public String getVoucherDesc() {
                return voucherDesc;
            }

            public void setVoucherDesc(String voucherDesc) {
                this.voucherDesc = voucherDesc;
            }


            public String getVoucherTitle() {
                return voucherTitle;
            }

            public void setVoucherTitle(String voucherTitle) {
                this.voucherTitle = voucherTitle;
            }

            public double getDiscount() {
                return discount;
            }

            public String getUnit() {
                return unit;
            }

            public void setUnit(String unit) {
                this.unit = unit;
            }

            public void setDiscount(double discount) {
                this.discount = discount;
            }

            public String getDiscountStr() {
                return discountStr;
            }

            public void setDiscountStr(String discountStr) {
                this.discountStr = discountStr;
            }

            public String getDiscountDesc() {
                return discountDesc;
            }

            public void setDiscountDesc(String discountDesc) {
                this.discountDesc = discountDesc;
            }

            public String getVoucherType() {
                return voucherType;
            }

            public void setVoucherType(String voucherType) {
                this.voucherType = voucherType;
            }

            public String getVoucherTypeDesc() {
                return voucherTypeDesc;
            }

            public void setVoucherTypeDesc(String voucherTypeDesc) {
                this.voucherTypeDesc = voucherTypeDesc;
            }
        }

        public String getLimit() {
            return limit;
        }

        public void setLimit(String limit) {
            this.limit = limit;
        }

        public String getOffset() {
            return offset;
        }

        public void setOffset(String offset) {
            this.offset = offset;
        }

        public String getPageCount() {
            return pageCount;
        }

        public void setPageCount(String pageCount) {
            this.pageCount = pageCount;
        }

        public String getTotal() {
            return total;
        }

        public void setTotal(String total) {
            this.total = total;
        }

        public List<RowsBean> getRows() {
            return rows;
        }

        public void setRows(List<RowsBean> rows) {
            this.rows = rows;
        }

        public boolean isLastPage() {
            return lastPage;
        }

        public void setLastPage(boolean lastPage) {
            this.lastPage = lastPage;
        }
    }

    public class TypeCountBean {
        String typeFour;   //	已过期优惠券数目
        String typeThree;    //已使用优惠券数目
        String typeTwo;     //未使用优惠券数目

        public String getTypeFour() {
            return typeFour;
        }

        public void setTypeFour(String typeFour) {
            this.typeFour = typeFour;
        }

        public String getTypeThree() {
            return typeThree;
        }

        public void setTypeThree(String typeThree) {
            this.typeThree = typeThree;
        }

        public String getTypeTwo() {
            return typeTwo;
        }

        public void setTypeTwo(String typeTwo) {
            this.typeTwo = typeTwo;
        }
    }

    public VoucherListBean getList() {
        return list;
    }

    public void setList(VoucherListBean list) {
        this.list = list;
    }

    public TypeCountBean getTypeCount() {
        return typeCount;
    }

    public void setTypeCount(TypeCountBean typeCount) {
        this.typeCount = typeCount;
    }
}
