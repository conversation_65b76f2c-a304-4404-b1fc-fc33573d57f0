package com.ybm100.app.crm.bean.drugstore.minedrugstore

class ApplyInitBean {

    var merchantName: String? = null // 客户名称
    var invoiceOriginalType: String? = null // 发票原始类型
    var invoiceOriginalTypeStr: String? = null // 发票原始类型字符串
    var merchantId: String? = null // 药店id
    var invoiceTypeList: List<InvoiceType>? = null // 可选发票类型
    var isFirst: Int = 0 //1：首营 2：非首营
    var isRedirect: Int = 0 // 0：不跳转 1：跳转
    var firstLicenseType: Int = 0//首营资质类型：1诊所 2药店 3新药店开办 4药店筹建
    var customerType: String? = ""// v2.6.5 最新资质类型
    var licenseBtn: Int = -1  //0首营状态 2资质变更 3 不能跳转

    class InvoiceType {
        var invoiceType: String? = null // 发票类型
        var invoiceTypeStr: String? = null // 发票类型字符串
    }

}