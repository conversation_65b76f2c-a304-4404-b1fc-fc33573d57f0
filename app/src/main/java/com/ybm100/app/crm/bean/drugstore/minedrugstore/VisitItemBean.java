package com.ybm100.app.crm.bean.drugstore.minedrugstore;

import java.io.Serializable;

/**
 * author :lx
 * date 2018/12/27.
 * email： <EMAIL>
 */
public class VisitItemBean implements Serializable {


    /**
     * id : 977
     * scheduleType : 1
     * scheduleTypeName : 电话拜访
     * scheduleTheme : 上门拜访温州集善大药房
     * userId : 2303
     * userName : CRM-DEV
     * startTime : 1564451340000
     * endTime : 1564454940000
     * createTime : 1564451457000
     * image :
     * merchantId : 87408
     * merchantName : 温州集善大药房
     * effective : 0
     * isKp : 1
     */

    private long id;
    private String scheduleType;
    private String scheduleTypeName;
    private String scheduleTheme;
    private int userId;
    private String userName;
    private long startTime;
    private long endTime;
    private long createTime;
    private String image;
    private String merchantId;
    private String merchantName;
    private int effective;
    private int isKp;
    /*3.0.0*/
    private int perfect; //1待完善 2已完善
    private String contactor; //联系人
    private int isEffective;//是否kp 1是 2否
    private long talkTime;//通话时长
    private int registerFlag;//是否注册 1注册 2否
    private String poiId;//客户id
    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getContactor() {
        return contactor;
    }

    public void setContactor(String contactor) {
        this.contactor = contactor;
    }

    public int getIsEffective() {
        return isEffective;
    }

    public void setIsEffective(int isEffective) {
        this.isEffective = isEffective;
    }

    public long getTalkTime() {
        return talkTime;
    }

    public void setTalkTime(long talkTime) {
        this.talkTime = talkTime;
    }

    public int getRegisterFlag() {
        return registerFlag;
    }

    public void setRegisterFlag(int registerFlag) {
        this.registerFlag = registerFlag;
    }

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }

    public int getPerfect() {
        return perfect;
    }

    public void setPerfect(int perfect) {
        this.perfect = perfect;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getScheduleType() {
        return scheduleType;
    }

    public void setScheduleType(String scheduleType) {
        this.scheduleType = scheduleType;
    }

    public String getScheduleTypeName() {
        return scheduleTypeName;
    }

    public void setScheduleTypeName(String scheduleTypeName) {
        this.scheduleTypeName = scheduleTypeName;
    }

    public String getScheduleTheme() {
        return scheduleTheme;
    }

    public void setScheduleTheme(String scheduleTheme) {
        this.scheduleTheme = scheduleTheme;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public int getEffective() {
        return effective;
    }

    public void setEffective(int effective) {
        this.effective = effective;
    }

    public int getIsKp() {
        return isKp;
    }

    public void setIsKp(int isKp) {
        this.isKp = isKp;
    }
}
