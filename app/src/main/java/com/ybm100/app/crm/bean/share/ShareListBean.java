package com.ybm100.app.crm.bean.share;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/6
 */
public class ShareListBean implements Serializable {
    /**
     * lastPage	是否最后一页	boolean	@mock=true
     * limit	每页显示条数	number	@mock=10
     * offset	页码	number	@mock=1
     * total	总条数	number	@mock=4
     */
    private boolean lastPage;
    private int limit;
    private int offset;
    private int total;
    private List<RowBean> rows;

    public static class RowBean {
        /**
         * branchCode	区域编码	string	@mock=XS420000
         * branchName	区域名称	string	@mock=HUBEI
         * shareImgUrl	生成的分享图片的URL	string
         * shareTime	分享次数	number	@mock=0
         * sourceDetail	素材详情	string	@mock=FSFJISLA;FJASFKLJS;ALFKJSFL
         * sourceImg	素材图片（上传的图片地址）	string	@mock=
         * sourceName	素材名称	string	@mock=BBB
         * sourceType	素材类型	number	@mock=1
         * sourceTypeName	素材类型名称	string	@mock=
         * sourceUrl	资源URL	string	@mock=HTTP://www.baidu.com
         */
        private String id;
        private String branchCode;
        private String branchName;
        private String shareImgUrl;
        private int shareTime;
        private String sourceDetail;
        private String sourceImg;
        private String sourceName;
        private String sourceType;
        private String sourceTypeName;
        private String sourceUrl;

        public String getBranchCode() {
            return branchCode;
        }

        public void setBranchCode(String branchCode) {
            this.branchCode = branchCode;
        }

        public String getBranchName() {
            return branchName;
        }

        public void setBranchName(String branchName) {
            this.branchName = branchName;
        }

        public String getShareImgUrl() {
            return shareImgUrl;
        }

        public void setShareImgUrl(String shareImgUrl) {
            this.shareImgUrl = shareImgUrl;
        }

        public int getShareTime() {
            return shareTime;
        }

        public void setShareTime(int shareTime) {
            this.shareTime = shareTime;
        }

        public String getSourceDetail() {
            return sourceDetail;
        }

        public void setSourceDetail(String sourceDetail) {
            this.sourceDetail = sourceDetail;
        }

        public String getSourceImg() {
            return sourceImg;
        }

        public void setSourceImg(String sourceImg) {
            this.sourceImg = sourceImg;
        }

        public String getSourceName() {
            return sourceName;
        }

        public void setSourceName(String sourceName) {
            this.sourceName = sourceName;
        }

        public String getSourceType() {
            return sourceType;
        }

        public void setSourceType(String sourceType) {
            this.sourceType = sourceType;
        }

        public String getSourceTypeName() {
            return sourceTypeName;
        }

        public void setSourceTypeName(String sourceTypeName) {
            this.sourceTypeName = sourceTypeName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getSourceUrl() {
            return sourceUrl;
        }

        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        @Override
        public String toString() {
            return "RowBean{" +
                    "branchCode='" + branchCode + '\'' +
                    ", branchName='" + branchName + '\'' +
                    ", shareImgUrl='" + shareImgUrl + '\'' +
                    ", shareTime='" + shareTime + '\'' +
                    ", sourceDetail='" + sourceDetail + '\'' +
                    ", sourceImg='" + sourceImg + '\'' +
                    ", sourceName='" + sourceName + '\'' +
                    ", sourceType='" + sourceType + '\'' +
                    ", sourceTypeName='" + sourceTypeName + '\'' +
                    ", sourceUrl='" + sourceUrl + '\'' +
                    '}';
        }
    }

    public boolean isLastPage() {
        return lastPage;
    }

    public void setLastPage(boolean lastPage) {
        this.lastPage = lastPage;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<RowBean> getRows() {
        return rows;
    }

    public void setRows(List<RowBean> rows) {
        this.rows = rows;
    }

    @Override
    public String toString() {
        return "ShareListBean{" +
                "lastPage=" + lastPage +
                ", limit=" + limit +
                ", offset=" + offset +
                ", total=" + total +
                ", rows=" + rows +
                '}';
    }
}
