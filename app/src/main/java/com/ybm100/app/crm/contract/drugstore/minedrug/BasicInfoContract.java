package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.BasicInfo;
import com.ybm100.app.crm.bean.drugstore.RebateScheduleBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 */
public interface BasicInfoContract {
    interface IBaseInfoModel extends IBaseModel {
        Observable<RequestBaseBean<BasicInfo>> getAuditInfo(String merchantId);
        Observable<RequestBaseBean<RebateScheduleBean>> getConsumptionRebateSchedule(String merchantId);
    }

    interface IBaseInfoView extends IBaseActivity {
        void getAuditInfo(RequestBaseBean<BasicInfo> auditBean);
        void getConsumptionRebateScheduleOnSuccess(RequestBaseBean<RebateScheduleBean> auditBean);
    }
}
