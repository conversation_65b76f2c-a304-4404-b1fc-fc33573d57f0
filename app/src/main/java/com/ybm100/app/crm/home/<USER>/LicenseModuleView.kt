package com.ybm100.app.crm.home.module

import android.content.Context
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.xyy.common.util.ConvertUtils
import com.xyy.common.widget.statusview.StatusViewLayout
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.ContainerRuntime.router
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.home.audit.HomeAuditBean
import com.ybm100.app.crm.constant.RoleTypeConfig
import com.ybm100.app.crm.home.presenter.LicenseModulePresenter
import com.ybm100.app.crm.ui.adapter.home.audit.HomeAuditAdapter
import com.ybm100.app.crm.ui.fragment.lzcustomer.LzToastUtils
import com.ybm100.app.crm.utils.module.module.BaseModule
import java.util.*

class LicenseModuleView(context: Context) : BaseModule<LicenseModulePresenter>(context) {


    private var rvHomeAudit: RecyclerView? = null
    private var layoutStatusView: StatusViewLayout? = null
    private var homeAuditAdapter: HomeAuditAdapter? = null


    override fun getContentLayoutId(): Int {
        return R.layout.layout_home_module_license
    }

    override fun onInit() {
//        if (RoleTypeConfig.isGJRGrop()) {
//            hideSelf()
//            return
//        }

        rvHomeAudit = fv(R.id.rv_home_audit)
        layoutStatusView = fv(R.id.layout_status_view)

        initAdapter()
        initDefaultLayout()

        fv<View>(R.id.tv_audit_more)?.setOnClickListener {
            if (RoleTypeConfig.isLzType()) {
                LzToastUtils.show()
            } else {
//                startActivity(AptitudeListActivity::class.java)
                ContainerRuntime.getFlutterRouter().open(context, "/licence_manager_page")
                UserBehaviorTrackingUtils.track("mc-homepage-qualifications-more")
            }
        }
    }

    private fun initAdapter() {
        rvHomeAudit?.isNestedScrollingEnabled = false
        rvHomeAudit?.layoutManager = LinearLayoutManager(context)
        homeAuditAdapter = HomeAuditAdapter(R.layout.item_home_audit)
        rvHomeAudit?.adapter = homeAuditAdapter
        homeAuditAdapter!!.setOnItemClickListener { adapter: BaseQuickAdapter<*, *>, view: View?, position: Int ->
            val item = adapter.getItem(position) as HomeAuditBean?
            if (item != null) {
//                ARouter.getInstance().build(RouterPath.LICENCE_DETAIL)
//                        .withString("licenseAuditId", item.code)
//                        .withString("type", item.type)
//                        .navigation()

                val sb = "/license_detail_page?" + "licenseAuditId=" +
                        item.code +
                        "&type=" +
                        item.type
                router.open(context, sb, null)
                UserBehaviorTrackingUtils.track("mc-homepage-qualifications")
            }
        }
    }

    /**
     * 初始化缺省页
     */
    private fun initDefaultLayout() {
        layoutStatusView?.let {
            it.setImageViewParams(330, 330)
            it.setEmptyDrawable(R.drawable.icon_empty)
            it.setPadding(paddingLeft, paddingTop, paddingRight, ConvertUtils.dp2px(10f))
            it.setOnRetryListener { requestHomeAudit() }
            it.showEmpty("暂无审核")
        }

    }

    /**
     * 0草稿
     * 5资质不合格
     * 10一审不通过
     */
    private fun requestHomeAudit() {
        val fieldMap: MutableMap<String?, Any?> = HashMap()
        fieldMap["limit"] = 3
        fieldMap["offset"] = 0
        fieldMap["statusStr"] = "0,5,10"
        mPresenter?.requestAudit(fieldMap)
    }

    override fun onRefresh() {
//        if (RoleTypeConfig.isGJRGrop()) {
//            return
//        }
        requestHomeAudit()
    }


    fun onRequestAuditSuccess(bean: List<HomeAuditBean?>?) {
        refreshCallback?.refreshFinish(typeId, null)
        if (bean == null || bean.isEmpty()) {
            layoutStatusView?.showEmpty("暂无审核")
            return
        }
        layoutStatusView?.showContent()
        if (bean.size < 4) {
            homeAuditAdapter?.setNewData(bean.subList(0, bean.size))
        } else {
            homeAuditAdapter?.setNewData(bean.subList(0, 3))
        }
    }

    override fun showNetError() {
        super.showNetError()
        refreshCallback?.refreshFinish(typeId, null)
        layoutStatusView?.showEmpty("暂无审核")
    }

    override fun getPresenter(): Class<LicenseModulePresenter>? {
        return LicenseModulePresenter::class.java
    }


}