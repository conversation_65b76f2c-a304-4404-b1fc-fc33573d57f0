package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.drugstore.BasicInfo;
import com.ybm100.app.crm.bean.drugstore.RebateScheduleBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.BasicInfoContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 */
public class BasicInfoModel extends BaseModel implements BasicInfoContract.IBaseInfoModel {

    public static BasicInfoModel newInstance() {
        return new BasicInfoModel();
    }

    @Override
    public Observable<RequestBaseBean<BasicInfo>> getAuditInfo(String merchantId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getAuditInfo(merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<RebateScheduleBean>> getConsumptionRebateSchedule(String merchantId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getConsumptionRebateSchedule(merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

}