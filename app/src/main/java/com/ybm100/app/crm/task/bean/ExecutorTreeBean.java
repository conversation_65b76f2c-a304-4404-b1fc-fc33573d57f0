package com.ybm100.app.crm.task.bean;

import java.util.List;

/**
 * Created by den<PERSON><PERSON><PERSON><PERSON> on 2019/1/3
 * 执行人树状
 */
public class ExecutorTreeBean {
    public String id;
    public int pid;
    public String name;
    public String tag;
    public List<ExecutorTreeBean> children;
    public ExecutorLevelItem.ParentUser parentUser;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ExecutorLevelItem.ParentUser getParentUser() {
        return parentUser;
    }

    public void setParentUser(ExecutorLevelItem.ParentUser parentUser) {
        this.parentUser = parentUser;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public List<ExecutorTreeBean> getChildren() {
        return children;
    }

    public void setChildren(List<ExecutorTreeBean> children) {
        this.children = children;
    }

    public boolean isPerson(){
        return "MAN".equals(tag);
    }
}
