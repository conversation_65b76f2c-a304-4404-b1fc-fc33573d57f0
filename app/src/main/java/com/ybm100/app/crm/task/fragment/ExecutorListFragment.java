package com.ybm100.app.crm.task.fragment;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;

import com.xyy.common.widget.DefaultItemDecoration;
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.task.bean.ExecutorLevelItem;
import com.ybm100.app.crm.task.adapter.ExecutorListAdapter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * Created by dengmingjia on 2019/1/13
 */
public class ExecutorListFragment extends BaseCompatFragment {
    @BindView(R.id.recyclerView)
    RecyclerView mRecyclerView;

    private ExecutorListAdapter mAdapter;
    private ExecutorLevelItem mItem;
    private ExecutorListAdapter.ActionListener mListener;
    private boolean mCanSelectP = true;
    private boolean mCanPickPerson = true;
    private boolean mCanPickGroup = true;
    private boolean mCanShowAll = true;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_executor_list;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        initRecyclerView();
    }

    private void initRecyclerView() {
        mRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mRecyclerView.addItemDecoration(new DefaultItemDecoration(getContext()));
        mAdapter = new ExecutorListAdapter();
        mAdapter.setCanPickPerson(mCanPickPerson);
        mAdapter.setCanPickGroup(mCanPickGroup);
        mAdapter.setCanShowAll(mCanShowAll);
        mRecyclerView.setAdapter(mAdapter);
        loadData();
    }

    private void loadData() {
        if (mAdapter == null) return;
        if (mListener != null) {
            mAdapter.setActionListener(mListener, mCanSelectP);
        }
        if (mItem != null) {
            List<ExecutorLevelItem> list = new ArrayList<>();
            ExecutorLevelItem allItem = null;
            if (mCanPickPerson) {
                allItem = mItem;
            } else {
                allItem = new ExecutorLevelItem(mItem.getId(), mItem.getContent(), mItem.isPerson(), mItem.getParent());
                if (mItem != null && mItem.getId() != null && mItem.getId().startsWith("P")) {
                    allItem.setPost(true);
                }
            }
            list.add(allItem);
            if (mItem.getChild() != null) {
                list.addAll(mItem.getChild());
            }
            mAdapter.setNewData(list);
        }
    }

    public void setData(ExecutorLevelItem item, ExecutorListAdapter.ActionListener lis, boolean canSelectP, boolean canPickPerson, boolean canPickGroup, boolean canShowAll) {
        mItem = item;
        mListener = lis;
        mCanSelectP = canSelectP;
        mCanPickPerson = canPickPerson;
        mCanPickGroup = canPickGroup;
        mCanShowAll = canShowAll;
        loadData();
    }

    public void refreshData() {
        if (mAdapter != null) mAdapter.notifyDataSetChanged();
    }

    @Override
    public boolean onBackPressedSupport() {
        if (mListener != null) {
            mListener.onItemPop(mItem);
        }
        return super.onBackPressedSupport();
    }
}
