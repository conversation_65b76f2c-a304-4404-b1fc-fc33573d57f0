package com.ybm100.app.crm.task.model;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.task.bean.PagingApiBean;
import com.ybm100.app.crm.task.bean.TaskExecutorBean;
import com.ybm100.app.crm.contract.BaseSearchContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by dengmingjia on 2018/12/25
 * 选择任务执行人
 */
public class TaskSearchExecutorModel extends BaseModel implements BaseSearchContract.IModel<TaskExecutorBean> {
    @Override
    public Observable<RequestBaseBean<PagingApiBean<TaskExecutorBean>>> search(String apiVersion, String keyword, int limit, int offset) {
        return RetrofitCreateHelper.createApi(ApiService.class).searchTaskExecutor(keyword, limit, offset)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
