package com.ybm100.app.crm.task.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.task.bean.TeamTaskBean
import com.ybm100.app.crm.task.contract.TeamTaskContract
import io.reactivex.Observable
import java.util.*

class TeamTaskModel : BaseModel(), TeamTaskContract.ITeamTaskModel {
    override fun getTeamTask(queryMap: Map<String, String>): Observable<RequestBaseBean<TeamTaskBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getTeamTask(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<TeamTaskBean?>?>())
        /*return RetrofitCreateHelper.createApiMock(ApiService::class.java).getTeamTask("https://rap.int.ybm100.com/mockjsdata/77/app/teamtask/disassem", queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<TeamTaskBean?>?>())*/
    }
}