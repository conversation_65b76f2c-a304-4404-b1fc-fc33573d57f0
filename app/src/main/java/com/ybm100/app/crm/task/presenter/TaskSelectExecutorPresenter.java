package com.ybm100.app.crm.task.presenter;

import com.ybm100.app.crm.task.bean.ExecutorTreeBean;
import com.ybm100.app.crm.task.bean.ExecutorLevelItem;
import com.ybm100.app.crm.task.bean.TaskExecutorBean;
import com.ybm100.app.crm.contract.BaseExecutorContract;
import com.ybm100.app.crm.task.model.TaskSelectExecutorModel;
import com.ybm100.app.crm.presenter.BaseExecutorPresenter;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.functions.Function;

/**
 * Created by XyyMvpSportTemplate on 01/02/2019 20:35
 */
public class TaskSelectExecutorPresenter extends BaseExecutorPresenter<ExecutorTreeBean> {

    public static TaskSelectExecutorPresenter newInstance() {
        return new TaskSelectExecutorPresenter();
    }

    @Override
    protected ExecutorLevelItem getLevelItem(ExecutorTreeBean treeBean, ExecutorLevelItem parent, boolean showParent) {
        if (treeBean == null) return null;
        ExecutorLevelItem levelItem = new ExecutorLevelItem(treeBean.getId(), treeBean.getName(), treeBean.isPerson(), parent);
        if (treeBean.getChildren() != null && treeBean.getChildren().size() > 0) {
            List<ExecutorLevelItem> list = getLevelItemList(treeBean.getChildren(), levelItem, showParent);
            if (treeBean.getParentUser() != null && showParent) {
                ExecutorLevelItem executorLevelItem = new ExecutorLevelItem(treeBean.getParentUser().getOaId(), treeBean.getParentUser().getRealname(), true, null);
                list.add(executorLevelItem);
            }
            levelItem.setChild(list);
        }
        return levelItem;
    }

    private List<ExecutorLevelItem> getLevelItemList(List<ExecutorTreeBean> treeBeans, ExecutorLevelItem parent, boolean showParent) {
        List<ExecutorLevelItem> result = new ArrayList<>();
        for (ExecutorTreeBean bean : treeBeans) {
            result.add(getLevelItem(bean, parent, showParent));
        }
        return result;
    }

    @Override
    protected BaseExecutorContract.IModel getModel() {
        return TaskSelectExecutorModel.newInstance();
    }

    public Observable<TaskExecutorBean> getSelectedObservable(ExecutorLevelItem data) {

        return getExecutorObservable(data).distinct().flatMap(new Function<ExecutorLevelItem, ObservableSource<TaskExecutorBean>>() {
            @Override
            public ObservableSource<TaskExecutorBean> apply(ExecutorLevelItem item) throws Exception {
                TaskExecutorBean taskExecutorBean = new TaskExecutorBean();
                taskExecutorBean.setId(item.getId());
                taskExecutorBean.setRealName(item.getContent());
                return Observable.just(taskExecutorBean);
            }
        });
    }

    public Observable<ExecutorLevelItem> getExecutorObservable(List<ExecutorLevelItem> data) {
        return map(Observable.fromIterable(data));
    }

    public Observable<ExecutorLevelItem> getExecutorObservable(ExecutorLevelItem data) {
        return map(Observable.just(data));
    }

    public Observable<ExecutorLevelItem> map(Observable<ExecutorLevelItem> observable) {
        return observable.concatMap(new Function<ExecutorLevelItem, ObservableSource<ExecutorLevelItem>>() {
            @Override
            public ObservableSource<ExecutorLevelItem> apply(ExecutorLevelItem item) throws Exception {
                if (item.isPerson()) {
                    if (item.isChecked()) {
                        TaskExecutorBean taskExecutorBean = new TaskExecutorBean();
                        taskExecutorBean.setId(item.getId());
                        taskExecutorBean.setRealName(item.getContent());
                        return Observable.just(item);
                    } else {
                        return Observable.empty();
                    }
                } else if (item.getChild() == null || item.getChild().size() == 0) {
                    return Observable.empty();
                } else {
                    return getExecutorObservable(item.getChild());
                }
            }
        });
    }

}
