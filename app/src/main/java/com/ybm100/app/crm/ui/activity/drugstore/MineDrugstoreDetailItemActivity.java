package com.ybm100.app.crm.ui.activity.drugstore;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.adapter.CommonPageAdapter;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.BasicInfo;
import com.ybm100.app.crm.bean.drugstore.RebateScheduleBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.contract.drugstore.minedrug.BasicInfoContract;
import com.ybm100.app.crm.flutter.CustomFlutterFragment;
import com.ybm100.app.crm.presenter.drugstore.minedrug.BasicInfoPresenter;
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.DrugstoreAptitudesFragment;
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.DrugstoreFluCopyFragment;
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.DrugstoreOrderRecordFragment;
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.InvoiceTypeFragment;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import io.flutter.embedding.android.FlutterFragment;

/**
 * 客户详情
 *
 * <AUTHOR>
 */
public class MineDrugstoreDetailItemActivity extends BaseMVPCompatActivity<BasicInfoPresenter> implements BasicInfoContract.IBaseInfoView {
    @BindView(R.id.tabLayout_mine_drugstore)
    SlidingTabLayout tabLayoutMineDrugstore;
    @BindView(R.id.viewpager)
    ViewPager viewPager;
    @BindView(R.id.tv_toolbar_title)
    TextView tv_title;
    @BindView(R.id.default_toolbar)
    Toolbar toolbar;
    @BindView(R.id.iv_right_search)
    ImageView ivSearch;
    @BindView(R.id.iv_right_filter)
    ImageView ivFilter;
    @BindView(R.id.ll_red_packet_tip)
    LinearLayout llRedPacketTip;
    @BindView(R.id.tv_red_packet_tip_text)
    TextView tvRedPacketTipText;

    private String[] titles;
    private List<Fragment> drugstoreFragments;
    //    //基本信息
    private CustomFlutterFragment drugstoreBaseInfoFragment;
    //    //基本信息 未注册
//    private YBMDrugstoreBaseInfoUnRegisterFragment drugstoreBaseInfoUnRegisterFragment;
    //药店透视
    private DrugstoreFluCopyFragment drugstoreFluCopyFragment;
    //订单记录
    private DrugstoreOrderRecordFragment drugstoreOrderRecordFragment;
    //药店资质
    private DrugstoreAptitudesFragment aptitudesFragment;
    //优惠券
//    private CouponListFragment couponListFragment;
    //发票类型
    private InvoiceTypeFragment invoiceTypeFragment;

    private int position = 0;
    private int registerFlag = 1; //默认已注册。未注册的值为2
    private String merchantId;
    private String merchantName;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_mine_drugstore;
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        try {
            titles = getResources().getStringArray(R.array.drugstore_title);
            drugstoreFragments = new ArrayList<>();
            merchantId = getIntent().getStringExtra("merchantId");
            merchantName = getIntent().getStringExtra(DrugstoreConstants.INTENT_KEY_DRUGSTORE_NAME);
            position = getIntent().getIntExtra("position", 0);
            registerFlag = getIntent().getIntExtra("registerFlag", 1);
        } catch (Exception ignore) {
            ToastUtils.showShort("页面参数错误");
            finish();
            return;
        }

        try {
            if (merchantId == null
                    && getIntent().getData() != null
                    && getIntent().getData().getQuery() != null
                    && !getIntent().getData().getQuery().isEmpty()) {
                merchantId = getIntent().getData().getQueryParameter("merchantId");
                merchantName = getIntent().getData().getQueryParameter("merchantName");
                String position = getIntent().getData().getQueryParameter("position");
                this.position = Integer.parseInt(position == null ? "0" : position);
                String registerFlagStr = getIntent().getData().getQueryParameter("registerFlag");
                this.registerFlag = Integer.parseInt(registerFlagStr == null ? "1" : registerFlagStr);
            }
        } catch (Exception e) {
            ToastUtils.showShort("页面参数错误1");
            finish();
        }

    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initToolBar();
        setContentView();
    }

    private void initToolBar() {
        if (TextUtils.isEmpty(merchantName)) {
            mPresenter.getAuditInfo(merchantId);
            mPresenter.getConsumptionRebateSchedule(merchantId);
        } else {
            tv_title.setText(merchantName);
        }
        tv_title.getPaint().setFakeBoldText(true);
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    CustomFlutterFragment createFlutterFragment() {
        return new FlutterFragment.NewEngineFragmentBuilder(CustomFlutterFragment.class)
                .initialRoute("/customer_basic_info_page?customerId=" + merchantId + "&registerFlag=" + registerFlag)
                .build();
    }

    /**
     * 设置tablayou 部分
     */
    public void setContentView() {
//        if (registerFlag == 1) {
//            drugstoreBaseInfoFragment = findFragment(DrugstoreBaseInfoFragment.class);
//            if (drugstoreBaseInfoFragment == null) {
//                //基本信息
//                drugstoreBaseInfoFragment = DrugstoreBaseInfoFragment.newInstance(merchantId);
//            }
//        } else {
//            //基本信息未注册
//            drugstoreBaseInfoUnRegisterFragment = findFragment(YBMDrugstoreBaseInfoUnRegisterFragment.class);
//            if (drugstoreBaseInfoUnRegisterFragment == null) {
//                drugstoreBaseInfoUnRegisterFragment = YBMDrugstoreBaseInfoUnRegisterFragment.newInstance(merchantId);
//            }
//        }
        //基本信息
        drugstoreBaseInfoFragment = createFlutterFragment();
        //订单记录
        drugstoreOrderRecordFragment = findFragment(DrugstoreOrderRecordFragment.class);
        if (drugstoreOrderRecordFragment == null) {
            //订单记录
            drugstoreOrderRecordFragment = DrugstoreOrderRecordFragment.newInstance(merchantId);
        }
        //优惠卷
//        couponListFragment = findFragment(CouponListFragment.class);
//        if (couponListFragment == null) {
//            //优惠卷
//            couponListFragment = CouponListFragment.newInstance(merchantId);
//        }
        //客户资质
        aptitudesFragment = findFragment(DrugstoreAptitudesFragment.class);
        if (aptitudesFragment == null) {
            //客户资质
            aptitudesFragment = DrugstoreAptitudesFragment.newInstance(merchantId);
        }
        //客户透视
        drugstoreFluCopyFragment = findFragment(DrugstoreFluCopyFragment.class);
        if (drugstoreFluCopyFragment == null) {
            //客户透视
            drugstoreFluCopyFragment = DrugstoreFluCopyFragment.newInstance(merchantId);
        }
        //发票类型
        invoiceTypeFragment = findFragment(InvoiceTypeFragment.class);
        if (invoiceTypeFragment == null) {
            //发票类型
            invoiceTypeFragment = InvoiceTypeFragment.newInstance(merchantId);
        }
//        if (registerFlag == 1) {
        drugstoreFragments.add(drugstoreBaseInfoFragment);
//        } else {
//            drugstoreFragments.add(drugstoreBaseInfoUnRegisterFragment);
//        }
        drugstoreFragments.add(drugstoreOrderRecordFragment);
//        drugstoreFragments.add(couponListFragment);
        drugstoreFragments.add(aptitudesFragment);
        drugstoreFragments.add(drugstoreFluCopyFragment);
        drugstoreFragments.add(invoiceTypeFragment);
        CommonPageAdapter viewPagerAdapter = new CommonPageAdapter(getSupportFragmentManager(), drugstoreFragments);
        viewPager.setAdapter(viewPagerAdapter);
//        viewPager.setOffscreenPageLimit(4);
        tabLayoutMineDrugstore.setViewPager(viewPager, titles);
        tabLayoutMineDrugstore.setSnapOnTabClick(true);
        setTab(position);
    }

    /**
     * 跳转对应tab
     *
     * @param position
     */
    public void setTab(int position) {
        if (viewPager != null) {
            viewPager.setCurrentItem(position,false);
        }
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return new BasicInfoPresenter();
    }

    @Override
    public void showNetError() {

    }

    @Override
    public void getAuditInfo(RequestBaseBean<BasicInfo> auditBean) {
        tv_title.setText(auditBean.getData().getMerchant().getRealName());
    }

    @Override
    public void getConsumptionRebateScheduleOnSuccess(RequestBaseBean<RebateScheduleBean> bean) {
        if (bean.getData() != null && bean.getData().getIsShow()) {
            llRedPacketTip.setVisibility(View.VISIBLE);
            String copywriter = bean.getData().getCopywriter();
            tvRedPacketTipText.setText(bean.getData().getCopywriter());
        }
    }
}
