package com.ybm100.app.crm.ui.adapter.drugstore;


import androidx.annotation.Nullable;

import com.xyy.common.widget.flowtag.adaper.BaseFlowAdapter;
import com.xyy.common.widget.flowtag.adaper.BaseTagHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.order.bean.TagBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/4
 */
public class FilterAdapter extends BaseFlowAdapter<TagBean, BaseTagHolder> {


    public FilterAdapter(int layoutResId, @Nullable List<TagBean> data) {
        super(layoutResId, data);
    }

    public FilterAdapter(@Nullable List<TagBean> data) {
        super(data);
    }

    public FilterAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseTagHolder tagHelper, TagBean item) {
        tagHelper.getView(R.id.tv_tagName).setSelected(item.isChecked());
        tagHelper.setText(R.id.tv_tagName, item.getTagName());

    }
}
