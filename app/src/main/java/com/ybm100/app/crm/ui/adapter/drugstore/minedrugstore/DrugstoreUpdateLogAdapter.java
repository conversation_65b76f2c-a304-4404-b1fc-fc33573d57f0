package com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.UpdateLogBean;
import com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.viewholder.InfoAddViewHolder;
import com.ybm100.app.crm.ui.adapter.drugstore.minedrugstore.viewholder.InfoChangeViewHolder;

import java.util.List;

/**
 * author :lx
 * date 2018/12/28.
 * email： <EMAIL>
 * 信息变更日志adapter
 * <AUTHOR>
 */
public class DrugstoreUpdateLogAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final List<UpdateLogBean> updateLogBeanList;
    private final Context context;

    public DrugstoreUpdateLogAdapter(List<UpdateLogBean> updateLogBeanList, Context context) {
        this.updateLogBeanList = updateLogBeanList;
        this.context = context;
    }

    public void addData(List<UpdateLogBean> updateLogBeanList) {
        this.updateLogBeanList.addAll(updateLogBeanList);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        //操作类型	number	1为变更，0为新增
        RecyclerView.ViewHolder viewHolder = null;
        switch (viewType) {
            case 0:
                viewHolder = new InfoAddViewHolder(LayoutInflater.from(context).inflate(R.layout.item_info_add, viewGroup, false));
                break;
            case 1:
                viewHolder = new InfoChangeViewHolder(LayoutInflater.from(context).inflate(R.layout.item_info_change, viewGroup, false));
                break;
            default:
                viewHolder = new InfoAddViewHolder(LayoutInflater.from(context).inflate(R.layout.item_info_add, viewGroup, false));
                break;
        }
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        switch (viewHolder.getItemViewType()) {
            case 0:
                InfoAddViewHolder infoAddViewHolder = (InfoAddViewHolder) viewHolder;
                infoAddViewHolder.bindAddData(updateLogBeanList.get(position), context);
                break;
            case 1:
                InfoChangeViewHolder changeViewHolder = (InfoChangeViewHolder) viewHolder;
                changeViewHolder.bindChangeData(updateLogBeanList.get(position), context);
                break;
            default:
                break;
        }
    }

    @Override
    public int getItemCount() {
        return updateLogBeanList == null ? 0 : updateLogBeanList.size();
    }

    @Override
    public int getItemViewType(int position) {
        return updateLogBeanList.get(position).getMethodType();
    }
}
