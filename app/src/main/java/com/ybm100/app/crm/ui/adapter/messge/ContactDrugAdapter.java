package com.ybm100.app.crm.ui.adapter.messge;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.BaseDrugInfoBean;

/**
 * <AUTHOR>
 * @version 1.0
 * @file ContactDrugAdapter.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class ContactDrugAdapter extends BaseQuickAdapter<BaseDrugInfoBean, BaseViewHolder> {


    public ContactDrugAdapter(int layoutResId) {
        super(layoutResId);
    }

    @Override
    protected void convert(BaseViewHolder helper, BaseDrugInfoBean item) {
        if (!TextUtils.isEmpty(item.getRealName())) {
            helper.setText(R.id.tv_drug_name, item.getRealName());
        }
    }
}
