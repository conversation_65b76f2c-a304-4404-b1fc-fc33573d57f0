package com.ybm100.app.crm.ui.adapter.messge

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.utilslibrary.utils.GlideLoadUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.message.SessionMSG
import com.ybm100.app.crm.net.BaseUrl
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @version 1.0
 * @file MsgAdapter.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
class MSGSessionAdapter(list: List<SessionMSG>?) : BaseQuickAdapter<SessionMSG, BaseViewHolder>(R.layout.item_msg_type, list) {
    private val regex = Regex("<[^>]+>")
    override fun convert(helper: BaseViewHolder, item: SessionMSG) {
        val iconView = helper.getView<ImageView>(R.id.iv_icon)
        var url = item.keFuAvatar
        if (url != null && !url.toLowerCase().startsWith("http")) {
            url = BaseUrl.getBaseUrl().replace("/app/crm/", "") + url
        }
        GlideLoadUtils.loadImg(mContext, url, iconView, R.drawable.icon_load_failed)

        helper.setVisible(R.id.iv_dot, ((item.unReadMsgNum ?: 0) > 0 ))
        helper.setText(R.id.tv_title, item.keFuName ?: "")

        helper.setText(R.id.tv_content, item.lastMsg?.content?.let {
            var result = it
            if(it.contains("<img ") && !it.contains("msg-card-file")){
                result = "图片消息"
            } else if (it.contains("<video ")) {
                result = "视频消息"
            } else if (it.contains("msg-card-file")) {
                result = "文件消息"
            } else {
                result = regex.replace(it, "")
            }
            result
        } ?: "")

        val dfHour = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        val dfDay = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        helper.setText(R.id.tv_date, item.lastMsg?.createTime?.let {
            var stringDate = dfDay.format(Date(it))
            if (stringDate.equals(dfDay.format(Date(System.currentTimeMillis())))){
                stringDate = dfHour.format(Date(it))
            }
            stringDate
        } ?: "--")
    }
}