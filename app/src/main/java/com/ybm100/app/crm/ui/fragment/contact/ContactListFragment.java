package com.ybm100.app.crm.ui.fragment.contact;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.util.ToastUtils;
import com.xyy.common.widget.statusview.StatusViewLayout;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.rxbus.Subscribe;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.contract.contact.ContactListContract;
import com.ybm100.app.crm.listener.RefreshCompleteListener;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.presenter.contact.ContactListPresenter;
import com.ybm100.app.crm.schedule.service.CallRecordManager;
import com.ybm100.app.crm.ui.activity.message.ContactActivity;
import com.ybm100.app.crm.ui.adapter.messge.ContactAdapter;
import com.ybm100.app.crm.utils.router.RouterPath;
import com.ybm100.app.crm.widget.SideBar;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

import static com.ybm100.app.crm.constant.RxBusCode.RX_BUS_UPDATE_CONTACT_BOOK;
import static com.ybm100.app.crm.ui.activity.message.ContactActivity.EDIT;

/**
 * 联系人列表
 */
public class ContactListFragment extends BaseMVPCompatFragment<ContactListPresenter> implements ContactListContract.IContactListView, OnRefreshLoadMoreListener {

    private static final int CALL = 0x1;
    private StatusViewLayout mStatusView;
    private RecyclerView mRecyclerView;
    private ContactAdapter mAdapter;
    private View mAdd;
    private String merchantId;
    private boolean editable = true;
    private final boolean isCanRefresh = true;
    private RefreshCompleteListener completeListener;
    boolean first = true;
    String keyword = null;

    public static ContactListFragment newInstance(String string) {
        Bundle args = new Bundle();
        if (!TextUtils.isEmpty(string)) {
            args.putString("key", string);
        }

        ContactListFragment fragment = new ContactListFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof RefreshCompleteListener) {
            completeListener = (RefreshCompleteListener) context;
        }
    }

    @Override
    public void onSupportVisible() {
        super.onSupportVisible();
        RxPermissions rxPermissions = new RxPermissions(getActivity());
        Disposable subscribe = rxPermissions.requestEach(Manifest.permission.READ_CALL_LOG).subscribe(new Consumer<Permission>() {

            @Override
            public void accept(Permission permission) throws Exception {
                if (permission.granted) {
                    if (first) {
                        initDatas();
                        first = false;
                    }
                } else if (permission.shouldShowRequestPermissionRationale) {
                    ToastUtils.showShort(getString(R.string.please_open_call_log_permission));
                } else {
                    PermissionUtil.showPermissionDialog(mContext, getString(R.string.phone_permission_name), false);
                }
            }
        });
        addDisposable(subscribe);
    }


    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return ContactListPresenter.newInstance();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_contact_list;
    }

    @Override
    public void initUI(View view, @Nullable Bundle savedInstanceState) {
        keyword = getArguments().getString("key");
        merchantId = getArguments().getString("merchantId");
        editable = getArguments().getBoolean(EDIT);
        mStatusView = view.findViewById(R.id.svl_contact_view);

        SideBar mSideBar = view.findViewById(R.id.sb_contact);
        mAdd = view.findViewById(R.id.iv_contact_create);
        if (!TextUtils.isEmpty(merchantId)) {//药店联系人创建的有药店id，不需要加号的添加联系人
            mAdd.setVisibility(View.GONE);
        } else {
            mAdd.setVisibility(View.VISIBLE);
        }
        mAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ARouter.getInstance()
                        .build(RouterPath.CONTACT_BOOK_PAGE)
                        .withInt(ContactActivity.TYPE, ContactActivity.CREATE_CONTACT)
                        .withBoolean(EDIT, editable)
                        .navigation();
            }
        });
        //设置右侧[A-Z]快速导航栏触摸监听
        mSideBar.setOnTouchingLetterChangedListener(new SideBar.OnTouchingLetterChangedListener() {

            @Override
            public void onTouchingLetterChanged(String s) {
                //该字母首次出现的位置
                if (mAdapter != null && s != null && !s.isEmpty()) {
                    int position = mAdapter.getPositionForSection(s.charAt(0));
                    if (position != -1) {
                        mRecyclerView.scrollToPosition(position);
                        LinearLayoutManager mLayoutManager =
                                (LinearLayoutManager) mRecyclerView.getLayoutManager();
                        mLayoutManager.scrollToPositionWithOffset(position, 0);
                    }
                }
            }
        });
        mRecyclerView = view.findViewById(R.id.rv_contact);
        initRecyclerView();
    }

    private void initRecyclerView() {
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(mContext);
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mRecyclerView.setLayoutManager(linearLayoutManager);
        mStatusView.setOnRetryListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showWaitDialog(mContext.getResources().getString(R.string.loading));
                if (mAdapter != null) {
                    mPresenter.getContacts(mAdapter.getData().size() == 0, keyword, merchantId);
                } else {
                    mPresenter.getContacts(true, keyword, merchantId);
                }
            }
        });
    }

    @Override
    public void onLazyInitView(@Nullable Bundle savedInstanceState) {
        super.onLazyInitView(savedInstanceState);
    }

    private void initDatas() {
        showWaitDialog(mContext.getResources().getString(R.string.loading));
        mPresenter.getContacts(true, keyword, merchantId);
    }

    @Override
    public void onSupportInvisible() {
        super.onSupportInvisible();
        RxBus.get().register(this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        RxBus.get().unRegister(this);
    }


    @Override
    public void loadMoreComplete() {
//        mRefresh.finishLoadMoreWithNoMoreData();
    }

    @Subscribe(code = RX_BUS_UPDATE_CONTACT_BOOK)
    public void updateContacts(ContactBean bean) {
        if (mAdapter == null) {
            ArrayList<ContactBean> contactBeans = new ArrayList<>();
            contactBeans.add(bean);
            List<MultiItemEntity> multiItemEntities = mPresenter.proceesList(contactBeans);
            fillData(multiItemEntities);
        } else {
            boolean contains = mAdapter.getData().contains(bean);
            if (contains) {
                int indexOf = mAdapter.getData().indexOf(bean);
                if (indexOf != -1) {
                    mAdapter.getData().set(indexOf, bean);
                }
                List<MultiItemEntity> data = mAdapter.getData();

                ArrayList<ContactBean> contactBeans = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    MultiItemEntity entity = data.get(i);
                    if (entity instanceof ContactBean) {
                        contactBeans.add((ContactBean) entity);
                    }
                }
                List<MultiItemEntity> multiItemEntities1 = mPresenter.proceesList(contactBeans);
                mAdapter.setNewData(multiItemEntities1);
            } else {
                List<MultiItemEntity> data = mAdapter.getData();
                ArrayList<ContactBean> contactBeans = new ArrayList<>();
                contactBeans.add(bean);
                for (int i = 0; i < data.size(); i++) {
                    MultiItemEntity entity = data.get(i);
                    if (entity instanceof ContactBean) {
                        contactBeans.add((ContactBean) entity);
                    }
                }
                List<MultiItemEntity> multiItemEntities1 = mPresenter.proceesList(contactBeans);
                mAdapter.setNewData(multiItemEntities1);
            }
        }
        mStatusView.showContent();
    }

    @Override
    public void renderList(boolean refresh, List<MultiItemEntity> result) {
        hideWaitDialog();
        stopRefresh();
        if (refresh && mAdapter != null)
            mAdapter.getData().clear();
        if (result == null || result.size() == 0) {
            mStatusView.showEmpty();
        } else {
            if (mAdapter == null || refresh) {
                fillData(result);
            } else {
                mAdapter.addData(result);
            }
            mStatusView.showContent();
//            mRefresh.finishRefresh();
//            mRefresh.finishLoadMore();
        }
    }

    boolean hasCallPhonePermission = false;
    boolean hasCallLogPermission = false;

    private void callUp(String phoneNo, String merchantId, String merchantName) {
        if (hasCallLogPermission && hasCallPhonePermission) {
            Intent intent = new Intent(Intent.ACTION_CALL);
            Uri data = Uri.parse("tel:" + phoneNo);
            intent.setData(data);
            startActivityForResult(intent, CALL);
            CallRecordManager.INSTANCE.addCallRecord(phoneNo, merchantId, merchantName, "contact_list");
        }
    }

    private void fillData(List<MultiItemEntity> result) {
        mRecyclerView.setAdapter(mAdapter = new ContactAdapter(result));
        mAdapter.setContactMoreAction(new ContactAdapter.ContactMoreAction() {
            @Override
            public void call(final String phoneNo, final String id, final String merchantName) {
                RxPermissions rxPermissions = new RxPermissions(getActivity());
                Disposable subscribe = rxPermissions
                        .requestEach(Manifest.permission.CALL_PHONE, Manifest.permission.READ_CALL_LOG)
                        .subscribe(new Consumer<Permission>() {
                            @Override
                            public void accept(Permission permission) throws Exception {
                                if (permission.granted) {
                                    if (Manifest.permission.CALL_PHONE.equals(permission.name)) {
                                        hasCallPhonePermission = true;
                                    } else {
                                        hasCallLogPermission = true;
                                    }
                                    callUp(phoneNo, id, merchantName);
                                } else if (permission.shouldShowRequestPermissionRationale) {
                                    if (Manifest.permission.CALL_PHONE.equals(permission.name)) {
                                        ToastUtils.showShort(getString(R.string.please_open_call_permission));
                                    } else {
                                        ToastUtils.showShort(getString(R.string.please_open_call_log_permission));
                                    }
                                } else {
                                    PermissionUtil.showPermissionDialog(mContext, getString(R.string.phone_permission_name), false);
                                }
                            }
                        });

                addDisposable(subscribe);
            }

            @Override
            public void delContact(final ContactBean entity, final int parentPosition) {
                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                builder.setMessage(R.string.del_contact).setPositiveButton(R.string.confirm, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mPresenter.delContact(entity, parentPosition);
                    }
                }).setNegativeButton(R.string.cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                    }
                });
                builder.show();
            }

            @Override
            public void editContact(ContactBean bean) {
                ARouter.getInstance()
                        .build(RouterPath.CONTACT_BOOK_PAGE)
                        .withInt(ContactActivity.TYPE, ContactActivity.CREATE_CONTACT)
                        .withSerializable("bean", bean)
                        .withBoolean(EDIT, editable)
                        .navigation();
            }

            @Override
            public void go2Detail(ContactBean bean) {
                ARouter.getInstance()
                        .build(RouterPath.CONTACT_BOOK_PAGE)
                        .withInt(ContactActivity.TYPE, ContactActivity.CONTACT_DETAIL)
                        .withSerializable("bean", bean)
                        .withBoolean(EDIT, editable)
                        .navigation();
            }
        });
        mAdapter.bindToRecyclerView(mRecyclerView);
    }

    @Override
    public void enableLoadMore(boolean b) {
//        mRefresh.setEnableLoadMore(b);
    }

    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        mPresenter.getContacts(false, keyword, merchantId);
    }

    @Override
    public void onRefresh(@NonNull RefreshLayout refreshLayout) {
        mPresenter.getContacts(true, keyword, merchantId);
    }

    public void requestData(String s) {
        this.keyword = s;
        mPresenter.getContacts(true, keyword, merchantId);
    }

    @Override
    public void updateData() {
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void removeContact(int parentPosition) {
        mAdapter.remove(parentPosition);
        if (mAdapter.getData().size() == 0) {
            mStatusView.showEmpty();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == CALL) {
            //查询太快查不出来
            Disposable subscribe = Observable.just(mAdapter)
                    .delay(500, TimeUnit.MILLISECONDS)
                    .subscribe(new Consumer<ContactAdapter>() {
                        @Override
                        public void accept(ContactAdapter contactAdapter) throws Exception {
                            mPresenter.queryCallLog(mAdapter.getItemCount(), mAdapter.getData());
                        }
                    });
            addDisposable(subscribe);

        }
    }

    @Override
    public void showNetError() {
        mStatusView.showNetWorkException();
    }

    private void stopRefresh() {
        if (completeListener != null)
            completeListener.refreshComplete();
    }

}
