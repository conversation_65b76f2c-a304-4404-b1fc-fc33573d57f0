package com.ybm100.app.crm.ui.fragment.hycustomer;


import android.os.Bundle;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.xyy.utilslibrary.adapter.CommonPageAdapter;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiUrl;
import com.ybm100.app.crm.bean.drugstore.BasicInfo;
import com.ybm100.app.crm.bean.drugstore.RebateScheduleBean;
import com.ybm100.app.crm.constant.DrugstoreConstants;
import com.ybm100.app.crm.contract.drugstore.minedrug.BasicInfoContract;
import com.ybm100.app.crm.presenter.drugstore.minedrug.BasicInfoPresenter;
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.DrugstoreBaseInfoUnRegisterFragment;
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.DrugstoreVisitRecordFragment;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * 荷叶健康客户详情item
 */
public class HyMineDrugstoreDetailItemActivity extends BaseMVPCompatActivity<BasicInfoPresenter> implements BasicInfoContract.IBaseInfoView {
    @BindView(R.id.tabLayout_mine_drugstore)
    SlidingTabLayout tabLayoutMineDrugstore;
    @BindView(R.id.viewpager)
    ViewPager viewPager;
    @BindView(R.id.tv_toolbar_title)
    TextView tv_title;
    @BindView(R.id.default_toolbar)
    Toolbar toolbar;
    @BindView(R.id.iv_right_search)
    ImageView ivSearch;
    @BindView(R.id.iv_right_filter)
    ImageView ivFilter;
    private String[] titles;
    private List<BaseCompatFragment> drugstoreFragments;
    //基本信息 未注册
    private DrugstoreBaseInfoUnRegisterFragment drugstoreBaseInfoUnRegisterFragment;
    //拜访记录
    private DrugstoreVisitRecordFragment drugstoreVisitRecordFragment;
    //荷叶销售数据
    private HyWebViewContainerFragment hyWebViewContainerFragment;
    //跟进BD信息
    private HyBDFollowUpInfoFragment hyBDFollowUpInfoFragment;

    private int position = 0;
    private String merchantId;
    private String merchantName;
    private String poiId;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_mine_drugstore;
    }

    @Override
    protected void initTransferData() {
        super.initTransferData();
        titles = getResources().getStringArray(R.array.hy_drugstore_title);
        drugstoreFragments = new ArrayList<>();
        merchantId = getIntent().getStringExtra("merchantId");
        poiId = getIntent().getStringExtra(DrugstoreConstants.INTENT_KEY_POI_ID);
        merchantName = getIntent().getStringExtra(DrugstoreConstants.INTENT_KEY_DRUGSTORE_NAME);
        position = getIntent().getIntExtra("position", 0);
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        initToolBar();
        setContentView();
    }

    private void initToolBar() {
        if (TextUtils.isEmpty(merchantName)) {
            mPresenter.getAuditInfo(merchantId);
        } else {
            tv_title.setText(merchantName);
        }
        tv_title.getPaint().setFakeBoldText(true);
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    /**
     * 设置tablayou 部分
     */
    public void setContentView() {
        //基本信息未注册
        drugstoreBaseInfoUnRegisterFragment = findFragment(DrugstoreBaseInfoUnRegisterFragment.class);
        if (drugstoreBaseInfoUnRegisterFragment == null) {
            drugstoreBaseInfoUnRegisterFragment = DrugstoreBaseInfoUnRegisterFragment.newInstance(merchantId, true,poiId);
        }

        //跟进记录
        drugstoreVisitRecordFragment = findFragment(DrugstoreVisitRecordFragment.class);
        if (drugstoreVisitRecordFragment == null) {
            //跟进记录
            drugstoreVisitRecordFragment = DrugstoreVisitRecordFragment.newInstance(merchantId);
        }
        //bd跟进信息
        hyBDFollowUpInfoFragment = findFragment(HyBDFollowUpInfoFragment.class);
        if (hyBDFollowUpInfoFragment == null) {
            //bd跟进信息
            hyBDFollowUpInfoFragment = HyBDFollowUpInfoFragment.newInstance(merchantId);
        }
        drugstoreFragments.add(drugstoreBaseInfoUnRegisterFragment);
        drugstoreFragments.add(drugstoreVisitRecordFragment);
        //荷叶销售数据
        hyWebViewContainerFragment = findFragment(HyWebViewContainerFragment.class);
        if (hyWebViewContainerFragment == null) {
            hyWebViewContainerFragment = HyWebViewContainerFragment.getInstance(ApiUrl.getHYSalesData(poiId));
        }
        drugstoreFragments.add(hyWebViewContainerFragment);
        drugstoreFragments.add(hyBDFollowUpInfoFragment);
        CommonPageAdapter viewPagerAdapter = new CommonPageAdapter(getSupportFragmentManager(), drugstoreFragments);
        viewPager.setAdapter(viewPagerAdapter);
        viewPager.setOffscreenPageLimit(1);
        tabLayoutMineDrugstore.setViewPager(viewPager, titles);
        setTab(position);
    }

    /**
     * 跳转对应tab
     *
     * @param position
     */
    public void setTab(int position) {
        if (viewPager != null) {
            viewPager.setCurrentItem(position);
        }
    }

    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return new BasicInfoPresenter();
    }

    @Override
    public void showNetError() {

    }

    @Override
    public void getAuditInfo(RequestBaseBean<BasicInfo> auditBean) {
        tv_title.setText(auditBean.getData().getMerchant().getRealName());
    }
    @Override
    public void getConsumptionRebateScheduleOnSuccess(RequestBaseBean<RebateScheduleBean> bean) {

    }
}
