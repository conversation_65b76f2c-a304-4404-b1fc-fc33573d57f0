<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ybm100.app.crm.goodsmanagement.widget.searchview.SearchView
        android:id="@+id/search_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:textColorSearch="@color/color_292933"
        app:textHintSearch="助记码/厂家/药品名称"
        app:textSizeSearch="13sp" />

    <TextView
        android:id="@+id/tv_search_history"
        android:layout_width="wrap_content"
        android:layout_height="23dp"
        android:layout_marginStart="15dp"
        android:layout_marginTop="15dp"
        android:gravity="center"
        android:text="历史搜索"
        android:textColor="@color/color_292933"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_view" />

    <TextView
        android:id="@+id/ib_search_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="15dp"
        android:drawableStart="@drawable/ic_tag_delete"
        android:gravity="center_vertical"
        android:text="清空"
        android:textColor="@color/color_9494A6"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_search_history"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_search_history" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_search_history" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_history"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_search_history,ib_search_delete,recycler_view"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>