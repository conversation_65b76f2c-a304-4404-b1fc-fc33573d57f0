<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include layout="@layout/toobar_goods_reconmend" />

    

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/tabLayout_mine_drugstore"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp44"
        android:background="#ffffff"
        app:tl_indicator_color="@color/text_color_35C561"
        app:tl_indicator_height="@dimen/dp2"
        app:tl_indicator_width="28dp"
        app:tl_tab_padding="15dp"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/text_color_292933"
        app:tl_textSelectSize="@dimen/sp16"
        app:tl_textUnselectColor="@color/text_color_666666"
        app:tl_textsize="@dimen/sp13"
        app:tl_underline_color="@color/color_line"
        app:tl_underline_height="0.5dp" />

    <!-- 红包提示视图 -->
    <LinearLayout

        android:id="@+id/ll_red_packet_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFEEEF"
        android:orientation="horizontal"
        android:padding="12dp"
        android:visibility="gone"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/xiadanfan"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/tv_red_packet_tip_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已返0.5元红包，再买189.67元再返0.8元红包"
            android:textColor="#FF6B35"
            android:textSize="14sp"
            android:gravity="center_vertical" />

    </LinearLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>