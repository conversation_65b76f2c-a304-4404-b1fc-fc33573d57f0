<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_change_password"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingTop="20dp"
        android:paddingEnd="15dp"
        android:paddingBottom="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_change_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_password"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:text="修改密码"
            android:textColor="@color/color_292933"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_right_arrow"
            app:layout_constraintBottom_toBottomOf="@+id/tv_change_password"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_change_password" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_horizontal_divider"
        style="@style/divider_line_style"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_change_password" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_about_us"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingTop="20dp"
        android:paddingEnd="15dp"
        android:paddingBottom="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view_horizontal_divider">

        <TextView
            android:id="@+id/tv_about_us"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_about_us"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:text="关于我们"
            android:textColor="@color/color_292933"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/ic_right_arrow"
            android:drawablePadding="10dp"
            android:text="版本号V1.0.0"
            android:textColor="@color/color_9494A6"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_about_us"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_about_us" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_horizontal_divider1"
        style="@style/divider_line_style"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_about_us" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_check_update"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingTop="20dp"
        android:paddingEnd="15dp"
        android:paddingBottom="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view_horizontal_divider1">

        <TextView
            android:id="@+id/tv_check_update"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_check_update"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:text="检查更新"
            android:textColor="@color/color_292933"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_right_arrow"
            app:layout_constraintBottom_toBottomOf="@+id/tv_check_update"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_check_update" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        style="@style/divider_line_style"
        android:id="@+id/view_horizontal_divider2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_check_update" />


    <com.xyy.common.widget.RoundTextView
        android:id="@+id/rtv_logout"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="15dp"
        android:gravity="center"
        android:text="退出登录"
        android:textColor="@color/color_9494A6"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_backgroundColor="@android:color/white"
        app:rv_cornerRadius="2dp"
        app:rv_strokeColor="@color/dk_color_E5E5E5"
        app:rv_strokeWidth="0.5dp" />

</androidx.constraintlayout.widget.ConstraintLayout>
