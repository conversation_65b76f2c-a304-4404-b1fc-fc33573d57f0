<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="15dp">

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="#ffc9c9c9"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_select_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:src="@drawable/ic_cart_unselect"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_divider" />

    <RelativeLayout
        android:id="@+id/rl_image_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp13"
        android:layout_marginTop="15dp"
        app:layout_constraintLeft_toRightOf="@+id/iv_select_status"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="85dp"
            android:layout_height="85dp"
            android:layout_centerInParent="true"
            android:src="@drawable/icon_default_avatar" />

        <TextView
            android:id="@+id/tv_sell_out"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/bg_icon_sell_out"
            android:gravity="center"
            android:textColor="@color/main_white"
            android:textSize="12sp"
            tools:text="已告罄" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp7"
        android:layout_marginTop="15dp"
        app:layout_constraintLeft_toRightOf="@id/rl_image_status"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_shop_name"
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/tv_buy_number"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="-"
            android:textColor="@color/text_color_333333"
            android:textSize="15sp"
            tools:text="顺尔宁孟鲁司特钠片" />

        <TextView
            android:id="@+id/tv_buy_number"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:layout_below="@+id/tv_spec"
            android:layout_alignParentRight="true"
            android:layout_marginTop="-3dp"
            android:layout_marginRight="10dp"
            android:includeFontPadding="false"
            android:textColor="@color/text_color_333333"
            android:textSize="14sp"
            tools:text="X15" />

        <TextView
            android:id="@+id/tv_spec"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_shop_name"
            android:layout_marginTop="@dimen/shop_common_top_02"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/tv_buy_number"
            android:includeFontPadding="false"
            android:text="-"
            android:textColor="@color/text_color_8E8E93"
            android:textSize="12sp"
            tools:text="0.4克*36粒/盒" />


        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="17dp"
            android:layout_below="@+id/tv_spec"
            android:layout_alignParentLeft="true"
            android:layout_marginTop="@dimen/dp3"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/tv_buy_number"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:text="0.00"
            android:textColor="@color/text_color_8E8E93"
            android:textSize="12sp"
            tools:text="¥50.00" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_price"
            android:layout_alignParentStart="true"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="15dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_price_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/recycler_view_label"
            android:layout_marginTop="2dp"
            android:includeFontPadding="false"
            android:text="合计总价："
            android:textColor="@color/text_color_333333"
            android:textSize="@dimen/sp12" />

        <TextView
            android:id="@+id/tv_total_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_manufacturer"
            android:layout_alignBaseline="@id/tv_price_tips"
            android:layout_marginTop="2dp"
            android:layout_toRightOf="@id/tv_price_tips"
            android:includeFontPadding="false"
            android:textColor="@color/main_red"
            android:textSize="@dimen/sp16"
            tools:text="¥550.00" />

        <TextView
            android:id="@+id/tv_estimated_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_total_price"
            android:layout_alignParentStart="true"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="15dp"
            android:singleLine="true"
            android:textColor="@color/color_FF2121"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="买XXX件预估到手价99999.99元"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/tv_shop_name_real"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_estimated_price"
            android:layout_alignParentStart="true"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="15dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:drawableLeft="@drawable/ic_shop_name"
            android:drawablePadding="3dp"
            android:textColor="@color/color_676773"
            android:textSize="12sp"
            android:visibility="visible"
            tools:text="湖北小药药自营期间店" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
