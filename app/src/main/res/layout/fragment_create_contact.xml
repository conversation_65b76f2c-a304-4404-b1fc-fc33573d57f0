<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_create_contact_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/ll_block_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:descendantFocusability="blocksDescendants">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_contact_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <View
                        style="@style/normal_horizontal_line"
                        android:layout_height="@dimen/dp11" />

                    <EditText
                        android:id="@+id/aie_contact_last_name"
                        style="@style/contact_input_style"
                        android:hint="@string/please_input_contact_last_name"
                        android:maxLength="20" />

                    <View style="@style/normal_horizontal_line" />

                    <EditText
                        android:id="@+id/aie_contact_phone"
                        style="@style/contact_input_style"
                        android:hint="@string/please_input_contact_phone_no"
                        android:inputType="number"
                        android:maxLength="12" />

                    <View style="@style/normal_horizontal_line" />

                </LinearLayout>

                <AutoCompleteTextView
                    android:id="@+id/aie_drug_store_name"
                    style="@style/contact_input_style"
                    android:layout_below="@id/ll_contact_name"
                    android:hint="@string/please_input_drug_store_name" />

                <View
                    android:id="@+id/aie_drug_store_name_dividerline"
                    style="@style/normal_horizontal_line"
                    android:layout_height="@dimen/dp11"
                    android:layout_below="@id/aie_drug_store_name" />

                <RelativeLayout
                    android:id="@+id/rl_contact_sex"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/aie_drug_store_name_dividerline">

                    <com.ybm100.app.crm.widget.combinationview.ActInputEditView
                        android:id="@+id/aie_sex"
                        style="@style/input_style"
                        custom:input_arrow="true"
                        custom:input_divider="true"
                        custom:input_edit="false"
                        custom:input_edit_gravity_right="true"
                        custom:input_title="@string/sex"
                        custom:input_hint="@string/please_select"
                        custom:input_title_textsize="@dimen/dp16" />

                    <RadioGroup
                        android:id="@+id/rg_create_contact_sex"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/dp20"
                        android:orientation="horizontal"
                        android:visibility="invisible">

                        <RadioButton
                            android:id="@+id/rb_male_contact"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/male" />

                        <RadioButton
                            android:id="@+id/rb_female_contact"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/female" />
                    </RadioGroup>
                </RelativeLayout>

                <com.ybm100.app.crm.widget.combinationview.ActInputEditView
                    android:id="@+id/aie_contact_birthday"
                    style="@style/input_style"
                    android:layout_below="@id/rl_contact_sex"
                    custom:input_arrow="true"
                    custom:input_divider="true"
                    custom:input_edit="false"
                    custom:input_edit_gravity_right="true"
                    custom:input_title="@string/birthday"
                    custom:input_hint="@string/please_select"
                    custom:input_title_textsize="@dimen/dp16" />

                <com.ybm100.app.crm.widget.combinationview.ActInputEditView
                    android:id="@+id/aie_contact_position"
                    style="@style/input_style"
                    android:layout_below="@id/aie_contact_birthday"
                    custom:input_arrow="true"
                    custom:input_divider="true"
                    custom:input_edit="false"
                    custom:input_edit_gravity_right="true"
                    custom:input_title="@string/job_position"
                    custom:input_hint="@string/please_select"
                    custom:input_title_textsize="@dimen/dp16" />

                <TextView
                    android:id="@+id/tv_create_contact_tag"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp33"
                    android:layout_below="@id/aie_contact_position"
                    android:background="@color/background_gray"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/dp11"
                    android:text="@string/tag"
                    android:textColor="@color/text_color_666666"
                    android:textSize="@dimen/dp13" />


                <com.zhy.view.flowlayout.TagFlowLayout
                    android:id="@+id/tfl_contact_create_tags"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/ll_contact_tag"
                    android:layout_marginLeft="@dimen/dp5"
                    android:layout_marginRight="@dimen/dp5" />


                <LinearLayout
                    android:id="@+id/ll_contact_tag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_create_contact_tag"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/dp10"
                    android:paddingRight="@dimen/dp10">

                    <com.xyy.common.widget.RoundEditText
                        android:id="@+id/ret_create_contact_detail"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp44"
                        android:layout_weight="1"
                        android:hint="@string/tags_less_than_eight"
                        android:maxLength="8"
                        android:textColorHint="#C1C1C1"
                        android:textSize="@dimen/dp16" />

                    <TextView
                        android:id="@+id/iv_create_contact_add_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/confirm"
                        android:textColor="#8E8E93"
                        android:textSize="@dimen/dp16" />
                </LinearLayout>

                <View
                    style="@style/normal_horizontal_line"
                    android:layout_above="@id/ll_contact_tag" />

                <View
                    style="@style/normal_horizontal_line"
                    android:layout_below="@id/ll_contact_tag" />

            </RelativeLayout>
        </LinearLayout>
    </ScrollView>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_contact_drug"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:visibility="invisible"
        tools:visibility="gone" />
</RelativeLayout>