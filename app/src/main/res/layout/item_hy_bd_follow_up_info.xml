<?xml version="1.0" encoding="utf-8"?>
<com.xyy.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:background="@color/white"
    android:minHeight="68dp"
    android:orientation="horizontal"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="@dimen/dp7">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingLeft="15dp"
        android:paddingTop="14dp"
        android:paddingBottom="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tv_status"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:lineSpacingExtra="3dp"
            android:maxLines="2"
            android:paddingRight="24dp"
            android:textColor="@color/text_color_333333"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="张大胖" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:textColor="@color/text_color_666666"
            android:textSize="12sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="开始跟进客户时间：2018.11.11 13:30-14:40" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</com.xyy.common.widget.RoundLinearLayout>
