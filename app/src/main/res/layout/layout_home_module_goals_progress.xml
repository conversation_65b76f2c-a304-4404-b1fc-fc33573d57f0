<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="15dp"
    android:layout_marginTop="0dp"
    android:layout_marginEnd="15dp"
    android:layout_marginBottom="5dp"
    android:background="@drawable/shape_home_up_bg"
    android:paddingStart="10dp"
    android:paddingEnd="10dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="目标进度"
        android:textColor="@color/color_292933"
        android:textSize="17sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_question_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:src="@drawable/ic_question_mark"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toEndOf="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:drawableEnd="@drawable/ic_home_goals_arrow_down"
        android:drawablePadding="1dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:textColor="@color/color_292933"
        android:textSize="14sp"
        android:includeFontPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="2020年8月" />

    <TextView
        android:id="@+id/tv_last_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/ic_home_goals_left_arrow"
        android:drawablePadding="3dp"
        android:paddingTop="5dp"
        android:paddingEnd="5dp"
        android:text="上月"
        android:textSize="12sp"
        android:textColor="@color/color_292933"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="@+id/tv_date"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_next_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableEnd="@drawable/ic_home_goals_right_arrow"
        android:drawablePadding="3dp"
        android:paddingStart="5dp"
        android:paddingTop="5dp"
        android:text="下月"
        android:textSize="12sp"
        android:textColor="@color/color_292933"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="@+id/tv_date"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.xyy.common.widget.statusview.StatusViewLayout
        android:id="@+id/svl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_date">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                tools:itemCount="3"
                tools:listitem="@layout/item_home_module_goals_progress" />

            <TextView
                android:id="@+id/tv_expand"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="5dp"
                android:drawableStart="@drawable/ic_home_goals_expand"
                android:drawablePadding="3dp"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:paddingBottom="5dp"
                android:text="展开"
                android:textColor="@color/color_9494A6"
                android:textSize="12sp"
                android:visibility="visible" />
        </LinearLayout>

    </com.xyy.common.widget.statusview.StatusViewLayout>


</androidx.constraintlayout.widget.ConstraintLayout>