<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_address"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:visibility="gone"
    app:layout_constraintBottom_toTopOf="@id/iv_location"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    tools:visibility="visible">

    <com.xyy.common.widget.RoundLinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="265dp"
        android:minHeight="44dp"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp8"
        android:paddingTop="@dimen/dp10"
        android:paddingEnd="@dimen/dp8"
        android:paddingBottom="@dimen/dp10"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="@dimen/dp7">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp5"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_color_333333"
            android:textSize="14sp"
            tools:text="门店名称：光谷大药房" />

        <TextView
            android:id="@+id/tv_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_color_333333"
            android:textSize="14sp"
            tools:text="门店地址：武汉市洪山区光谷大道232号" />
    </com.xyy.common.widget.RoundLinearLayout>

    <ImageView
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/ic_white_down" />

</LinearLayout>