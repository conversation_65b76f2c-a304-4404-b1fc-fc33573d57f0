// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_out_of_stock_items_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OutOfStockItemsOther _$OutOfStockItemsOtherFromJson(
    Map<String, dynamic> json) =>
    OutOfStockItemsOther(
      requestId: json['requestId'] as String?,
      qt_list_data: json['qt_list_data'] as String?,
    );

Map<String, dynamic> _$OutOfStockItemsOtherToJson(
    OutOfStockItemsOther instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      'qt_list_data': instance.qt_list_data,
    };

OutOfStockItemsResponseModel _$OutOfStockItemsResponseModelFromJson(
    Map<String, dynamic> json) {
  return OutOfStockItemsResponseModel()
    ..total = json['total']
    ..offset = json['offset']
    ..limit = json['limit']
    ..rows = (json['rows'] as List<dynamic>?)
            ?.map((e) =>
                CommodityDetailGoodItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        []
    ..pageCount = json['pageCount']
    ..lastPage = json['lastPage']
    ..currentPage = json['currentPage']
    ..currentPageTmp = json['currentPageTmp']
    ..requestParameters = json['requestParameters']
    ..requestUrl = json['requestUrl']
    ..unReadCount = json['unReadCount']
    ..other = json['other'] == null
        ? null
        : OutOfStockItemsOther.fromJson(json['other'] as Map<String, dynamic>)
    ..otherTotal = json['otherTotal'];
}

Map<String, dynamic> _$OutOfStockItemsResponseModelToJson(
    OutOfStockItemsResponseModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'offset': instance.offset,
      'limit': instance.limit,
      'rows': instance.rows,
      'pageCount': instance.pageCount,
      'lastPage': instance.lastPage,
      'currentPage': instance.currentPage,
      'currentPageTmp': instance.currentPageTmp,
      'requestParameters': instance.requestParameters,
      'requestUrl': instance.requestUrl,
      'unReadCount': instance.unReadCount,
      'other': instance.other?.toJson(),
      'otherTotal': instance.otherTotal,
    };
