import 'package:flutter/material.dart';

class CustomeroutOfStockItemsFilter extends StatefulWidget {
  final ValueChanged<Map<String, dynamic>> changeFilter;

  CustomeroutOfStockItemsFilter({required this.changeFilter});
  @override
  State<StatefulWidget> createState() {
    return CustomeroutOfStockItemsFilterState();
  }
}

class CustomeroutOfStockItemsFilterState extends State<CustomeroutOfStockItemsFilter> {
  Map<String, dynamic> params = {};

  @override
  Widget build(BuildContext context) {
    return SizedBox.shrink(); // 完全不显示筛选器
  }
}
