import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class OutOfStockItemsFooter extends Footer {
  @override
  Widget contentBuilder(
      BuildContext context,
      LoadMode loadState,
      double pulledExtent,
      double loadTriggerPullDistance,
      double loadIndicatorExtent,
      AxisDirection axisDirection,
      bool float,
      Duration? completeDuration,
      bool enableInfiniteLoad,
      bool success,
      bool noMore) {
    
    Widget child;
    
    if (noMore) {
      child = Text(
        '没有更多了～',
        style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
      );
    } else if (loadState == LoadMode.load || loadState == LoadMode.armed) {
      child = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF9494A6)),
            ),
          ),
          SizedBox(width: 8),
          Text(
            '加载中...',
            style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
          ),
        ],
      );
    } else if (loadState == LoadMode.loaded) {
      child = Text(
        '加载完成',
        style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
      );
    } else if (loadState == LoadMode.done) {
      child = Text(
        success ? '加载完成' : '加载失败',
        style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
      );
    } else {
      child = Text(
        '上拉加载更多',
        style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
      );
    }
    
    return Container(
      height: 50,
      alignment: Alignment.center,
      child: child,
    );
  }
}
