import 'dart:math';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/rendering.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:qt_common_sdk/qt_common_sdk.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/utils/global_random_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommended_products/data/customer_recommended_products_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommended_products/widget/customer_recommended_products_filter.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommended_products/widget/recommended_products_header.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommended_products/widget/recommended_products_footer.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommendation/page/goods_recommend_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_detail_list_item.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_rank_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_discount_price_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerRecommendedProductsPage extends BasePage {
  final dynamic merchantId;
  final dynamic customerId;

  CustomerRecommendedProductsPage({this.merchantId,this.customerId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerRecommendedProductsPageState();
  }
}

class CustomerRecommendedProductsPageState extends BaseState<CustomerRecommendedProductsPage> with EventBusObserver {
  List<CommodityDetailGoodItemModel> dataSource = [];
  String? id = Platform.isAndroid ? '4_1' : '4_2';
  UserInfoData? userInfo;
  String spme = '';
  EasyRefreshController refreshController = EasyRefreshController();
  int page = 0;
  bool isFirstSearchKeyword = false;
  bool hasMoreData = true; // 是否还有更多数据
  String requestId = '';
  String qt_list_data = '';
  bool shuaxin = true;
  Map<String, dynamic> fliterParams = {};
  int currentFilterOption = -1; // 当前选择的过滤选项
  String currentFilterTitle = "全部"; // 当前选择的过滤标题

  bool isLoading = false; // 新增加载标志

    // 新增：滚动控制器与可见性检测相关变量
  late ScrollController _scrollController;
  final Set<String> _reportedSkuIds = {};
  late double _screenHeight;
  final GlobalKey _sliverListKey = GlobalKey(); // 用于获取列表上下文
  // 在State中定义存储Key的列表（与dataSource一一对应）
  final List<GlobalKey> _itemKeys = [];
  // 缓存Item高度（索引 -> 高度）
  final Map<int, double> _itemHeights = {};

  @override
  void initState() {
    print('加载刷新');
    this.hasMoreData = true; // 强制设置为 true 用于测试
    if(GoodsRecommendPageState.intId == 0){
      this.trackPublicCustomerSelected();///初次进入触发曝光
    }
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onListScrolled);
    this.eventBus.addListener(
      observer: this,
      eventName: QT.qtYBMProductRecommendationPage + GoodsRecommendPageState.id,
      callback: (params) {
        print('收到曝光事件，参数1: $params ${QT.qtYBMProductRecommendationPage + GoodsRecommendPageState.id}');
        trackPublicCustomerSelected(); ///切换页面后重新曝光并重新商品曝光
        _reportedSkuIds.clear();
        _onListScrolled();
      },
    );
    void didChangeDependencies() {
      super.didChangeDependencies();
      // 初始化屏幕高度（修改后）
      _screenHeight = MediaQuery.of(context).size.height - 44; // 移除 .h
  // 可视区域顶部计算（修改后）
      const visibleTop = 44; // 移除 .h
    }
    @override
    void dispose() {
      _removeEventListener(); // 最后一次移除，防止内存泄漏
      _scrollController.dispose(); // 销毁控制器
      super.dispose();
    }

    // 初始加载，显示系统级加载对话框
    WidgetsBinding.instance?.addPostFrameCallback((_) {
      if (mounted) {
        showLoadingDialog(dismissOnTap:false);
        this.requestList(isRefresh: false);
      }
    });
  }
  // 封装：移除事件监听
  void _removeEventListener() {
    print('尝试移除监听');
    eventBus.removeListener(
      observer: this,
      eventName: QT.qtYBMProductRecommendationPage,
      //callback: ,
    );
  }

  ///QT埋点
  void trackPublicCustomerSelected() async{
    try{
      var userInfo = await UserInfoUtil.getUserInfo();
      String spm = '';
      print('userInfo:${userInfo?.sessionId},${userInfo?.phone}');
      String sessionId = userInfo?.sessionId ?? '';
      spme = randomString6();
      spm = sessionId + spme;
      /// 手动埋点：商品推荐曝光页面曝光
      QTCommonSdk.onEvent("page_exposure", {
        'spm_cnt': '$id.prodRecommend' + '-0_0_TJP' + '.0.0.' +  spm,
      });
      print('商品推荐推荐品页面曝光埋点触发 ${GoodsRecommendPageState.intId}');
      GoodsRecommendPageState.intId = 1;
    }catch(e){
      print('页面曝光埋点上报失败--${e}');
    }
  }
  // 数据加载/刷新时初始化Key（确保Key与Item索引对应）
  Future<void> loadData(isRefresh) async {
    if (isRefresh) {
      _itemKeys.clear(); // 刷新时清空旧Key
    }
    _itemKeys.addAll(List.generate(this.dataSource.length, (_) => GlobalKey()));
    setState(() {});
  }
  /// 刷新数据
  Future<void> refreshMerchantData() async {
    print('🔄 Refresh started');
    this.page = 0;
    this.hasMoreData = true;
    this.requestId = '';
    //this.qt_list_data = '';
    showLoadingDialog(dismissOnTap:false); // 显示系统级加载对话框
     _reportedSkuIds.clear(); // 刷新时清空已上报记录
    await requestList(isRefresh: true); // 进行数据请求
    refreshController.finishRefresh();

    // 强制更新状态以确保上拉加载功能正常
    if (mounted) {
      setState(() {});
    }
    print('✅ Refresh finished, hasMoreData: $hasMoreData');
  }

  /// 加载更多数据
  Future<void> loadMoreData() async {
    if (!hasMoreData) {
      await refreshMerchantData(); // 触发下拉刷新逻辑
      refreshController.finishLoad(noMore: !hasMoreData);
      return;
    }
    this.page++;
    showLoadingDialog(dismissOnTap:false);// 显示系统级加载对话框
    await requestList(isRefresh: false);
    refreshController.finishLoad(success: true, noMore: !hasMoreData);
  }

  @override
  Widget buildWidget(BuildContext context) {
    print('🏗️ Building widget, hasMoreData: $hasMoreData, dataSource.length: ${dataSource.length}');
    print('🔧 EasyRefresh onLoad: ${hasMoreData ? "enabled" : "disabled"}');
    return Container(
      color: Color(0xFFF7F7F8),
      child: Column(
        children: [
          CustomerRecommendedProductsFilter(
            changeFilter: (params) async {
              this.fliterParams = params;
              this.page = 0;
              this.hasMoreData = true;
              this.requestId = '';

              // 更新当前过滤条件的 option 和 title
              if (params.containsKey('queryType')) {
                currentFilterOption = params['queryType'];
              } else {
                currentFilterOption = -1;
              }
              currentFilterTitle = _getFilterTitleByOption(currentFilterOption);
              this.refreshController.callRefresh();
            },
          ),
          Expanded(
            child: EasyRefresh.custom(
              scrollController: _scrollController,
              onRefresh: refreshMerchantData,
              onLoad: loadMoreData,
              header: RecommendedProductsHeader(),
              footer: RecommendedProductsFooter(),
              emptyWidget: getEmptyWidget(),
              controller: refreshController,
              enableControlFinishRefresh: true,
              enableControlFinishLoad: true,
              slivers: [
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                        (context, index) {
                      CommodityDetailGoodItemModel model = dataSource[index];
                      return GestureDetector(
                        key: ValueKey('commodity_${model.skuId}_$index'),
                        onTap: () async{
                          var skuId = model.skuId;
                          String router =
                              "xyy://crm-app.ybm100.com/good_detail?id=$skuId&isFromCustomer=1";
                          // if (widget.provinceCode != -1) {
                          //   router = router + "&branchCode=XS${widget.provinceCode}";
                          // }
                          if (widget.customerId != null) {
                            router = router + "&merchantId=${widget.customerId}";
                          }
                          try{
                            var userInfo = await UserInfoUtil.getUserInfo();
                            var sessionId = userInfo?.sessionId ?? '';
                            String spm = '$sessionId$spme';
                            var qt_sku_data = model.qt_sku_data ?? '';
                            var skuJson = json.decode(qt_sku_data)as Map<String, dynamic>;
                            print('完整的JSON数据：$jsonDecode  $qt_sku_data');
                            var rank = skuJson['rank'] as int? ?? 0;
                            var exp_id = json.decode(qt_list_data)['exp_id']; // 修改这里
                            print('jsonDecode:${skuJson['rank']},$qt_sku_data,$exp_id');
                            var scme = randomString6();
                            QTCommonSdk.onEvent("action_list_product_click	", {
                              'spm_cnt': '$<EMAIL>@${_mapFilterOptionToSequence(currentFilterOption)}_prod@$rank.$spm',
                              'scm_cnt': 'recommend.$exp_id.all_0.prod-${model.skuId}.${requestId}$scme',
                              'product_id': model.skuId,
                              'product_name': model.skuName,
                              'qt_list_data': qt_list_data,
                              'qt_sku_data':model.qt_sku_data,
                            });
                          }catch(e){
                            print('点击上报埋点失败');
                          }
                          XYYContainer.open(router);
                        },
                        child: CommodityDetailListItem(
                          key: _itemKeys[index],
                          isMerchantEnter: isMerchantEnter(),
                          model: model,
                          onRendered: () {
                            _cacheItemHeight(index);
                          },
                        ),
                      );
                    },
                    childCount: dataSource.length,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 是否从客户详情进入
  bool isMerchantEnter() {
    return widget.merchantId?.isNotEmpty == true;
  }


  Future<void> requestList({bool isRefresh = false}) async {
    Map<String, dynamic> param = {
      'merchantId': widget.merchantId,
      'offset': this.page,
      'limit': 10,
      'requestId': this.requestId,
    };
    param.addAll(this.fliterParams);
    shuaxin = false;
    var result = await NetworkV2<RecommendedProductsResponseModel>(RecommendedProductsResponseModel())
        .requestDataV2(
      'skuRank/getSkuRecommendList',
      parameters: param,
    );

    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var source = data?.rows ?? [];

        // 判断是否还有更多数据 - 使用API返回的lastPage字段
        bool isLastPage = data?.lastPage == true;
        print('isLastPage:${isLastPage},${data?.lastPage}');
        this.hasMoreData = !isLastPage;
        print('📊 Received ${source.length} items, lastPage: $isLastPage, hasMoreData: $hasMoreData');
        if (this.page == 0) {
          this.dataSource = source;
          loadData(true);
        } else {
          this.dataSource.addAll(source);
          loadData(false);
        }
        setState(() {
          // 获取 requestId
          this.requestId = data?.other?.requestId ?? '';
          this.qt_list_data = data?.other?.qt_list_data ?? '';
        });
        // 如果是刷新操作且有更多数据，重置加载状态
        if (isRefresh && hasMoreData) {
          print('isRefresh ${isRefresh && hasMoreData}');
          refreshController.resetLoadState();
        }
        // 数据加载完成后检测可见商品
        WidgetsBinding.instance?.addPostFrameCallback((_) {  // 关键：用 ?. 替代 .
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) {
              shuaxin = true;
              print('加载完 上报埋点');
              _onListScrolled();
            }
          });
        });
        /// 关闭加载对话框
        dismissLoadingDialog();
      } else {
        shuaxin = true;
        // 请求失败时，如果是加载更多，需要回退页码
        if (this.page > 0) {
          this.page--;
        }
      }
    }
    return;
  }
  /// 构建到手价请求参数
  Map<String, dynamic> buildDiscountPriceParams() {
    var skuIds = dataSource.map((e) {
      return e.skuId;
    }).join(",");
    return {"idList": skuIds, "merchantId": widget.customerId};
  }
  /// 请求折扣价格信息
  Future<void> refreshDiscountPrice() async {
    try {
      var result = await NetworkV2<CommodityDiscountPriceData>(CommodityDiscountPriceData())
          .requestDataV2("product/manage/getDiscountPrice",
              parameters: buildDiscountPriceParams());

      if (mounted && result.isSuccess == true) {
        var listData = result.getData()?.rows;
        if (listData != null && listData.isNotEmpty) {
          print('💰 Received discount prices for ${listData.length} items');

          // 更新折扣价格
          for (var discountItem in listData) {
            try {
              var targetItem = dataSource.firstWhere((item) {
                return item.skuId.toString() == discountItem.id.toString();
              });
              targetItem.discountPrice = discountItem.discountPrice;
              print('💰 Updated discount price for skuId: ${discountItem.id} -> ${discountItem.discountPrice}');
            } catch (e) {
              print('⚠️ Could not find item with skuId: ${discountItem.id}');
            }
          }

          // 更新UI
          if (mounted) {
            setState(() {});
          }
        }
      }
    } catch (error) {
      print('❌ Error requesting discount prices: $error');
    }
  }

  /// 空页面
  Widget? getEmptyWidget() {
    return this.dataSource.length > 0
        ? null
        : PageStateWidget.pageEmpty(PageState.Empty);
  }

   /// 滚动监听：检测商品是否进入可视区域（兼容所有索引）
  void _onListScrolled() {
    // 1. 获取当前可见的Item索引列表（基于缓存高度+估算）
    final visibleIndices = _getVisibleIndices();
    if (visibleIndices.isEmpty) {
      print('滚动触发：无可见Item');
      return;
    }
    print('滚动触发：可见索引 $visibleIndices');

    // 2. 遍历可见索引，检查并上报埋点
    for (final index in visibleIndices) {
      // 安全校验：索引越界则跳过
      if (index < 0 || index >= dataSource.length) {
        continue;
      }

      final item = dataSource[index];
      final skuId = item.skuId.toString();

      // 已上报过则跳过
      if (_reportedSkuIds.contains(skuId)) {
        continue;
      }
      // 3. 用优化后的可见性判断逻辑（基于GlobalKey获取的实际位置）
      if (_isItemVisible(index)) {
         if(!shuaxin)return;///页面还没刷新直接不上报
        _reportCommodityExposure(item);
        _reportedSkuIds.add(skuId);
      }
    }
  }

  /// 优化：获取可见区域的Item索引（基于缓存高度+估算）
  List<int> _getVisibleIndices() {
    if (!_scrollController.hasClients || dataSource.isEmpty) {
      return [];
    }
    // 当前滚动偏移和屏幕高度
    final scrollOffset = _scrollController.offset;
    final screenHeight = MediaQuery.of(context).size.height;
    final visibleTop = scrollOffset;
    final visibleBottom = scrollOffset + screenHeight;
    final visibleIndices = <int>[];
    double currentOffset = 0.0; // 累计Item顶部偏移
    // 遍历所有Item，计算位置并判断是否在可视区域
    for (int i = 0; i < dataSource.length; i++) {
      // 获取Item高度（优先用缓存，无缓存则估算）
      final itemHeight = _getItemHeight(i);
      final itemTop = currentOffset;
      final itemBottom = currentOffset + itemHeight;
      // 判断Item与可视区域是否有交集
      if (itemBottom > visibleTop && itemTop < visibleBottom) {
        visibleIndices.add(i);
      }
      print('位置判断--商卡${i+ 1}底部位置${itemBottom}，顶部位置${itemTop},滚动距离${scrollOffset}, 滚动距离加上页面高度${visibleBottom}');
      // 累加偏移（准备计算下一个Item）
      currentOffset = itemBottom;
      // 优化：若当前偏移已超过可视区域底部，可提前退出循环
      if (currentOffset > visibleBottom + 200) { // 加200px缓冲
        break;
      }
    }

    return visibleIndices;
  }

  /// 优化：判断Item是否可见（基于GlobalKey获取实际位置）
  bool _isItemVisible(int index) {
    // 1. 校验索引和Key的有效性
    if (index < 0 || index >= _itemKeys.length) {
      return false;
    }
    final itemKey = _itemKeys[index];
    final itemContext = itemKey.currentContext;
    if (itemContext == null) {
      // Item尚未渲染，返回false（避免误判）
      return false;
    }

    // 2. 获取Item的实际位置（全局坐标）
    final renderBox = itemContext.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      return false;
    }
    final itemGlobalPos = renderBox.localToGlobal(Offset.zero);
    final itemTop = itemGlobalPos.dy;
    final itemBottom = itemTop + renderBox.size.height;

    // 3. 可视区域范围（顶部：筛选栏高度，底部：屏幕高度）
    const visibleTop = 44.0;
    final visibleBottom = MediaQuery.of(context).size.height;

    // 4. 判断可见性（根据业务需求调整：这里用“露出50%以上”）
    final visibleHeight = (itemBottom < visibleBottom ? itemBottom : visibleBottom) -
        (itemTop > visibleTop ? itemTop : visibleTop);
    return visibleHeight >= renderBox.size.height * 0.5;
  }
  // 缓存指定索引的Item高度
  void _cacheItemHeight(int index) {
    if (index < 0 || index >= _itemKeys.length) return;
    final key = _itemKeys[index];
    final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      _itemHeights[index] = renderBox.size.height;
      print('缓存高度：index=$index，height=${renderBox.size.height}');
    }
  }
  /// 获取指定索引Item的实际高度（通过RenderBox）
// 获取Item高度（优先用缓存，无缓存则估算）
  double _getItemHeight(int index) {
    // 1. 已缓存的高度直接返回
    if (_itemHeights.containsKey(index)) {
      return _itemHeights[index]!;
    }
    // 2. 未缓存则估算（基于商品类型）
    final model = dataSource[index];
    if (model.isSpellGroup) return 132;
    if (isMerchantEnter()) return 145;
    return 130;
  }

  // 新增：商品曝光埋点上报
  void _reportCommodityExposure(CommodityDetailGoodItemModel item) async {
    if(!shuaxin){
      print('嘻嘻嘻取消了');
      return;
    }
    print('成功上报商品埋点');
    try {
      var userInfo = await UserInfoUtil.getUserInfo();
      var sessionId = userInfo?.sessionId ?? '';
      String spm = '$sessionId$spme';
      var qt_sku_data = item.qt_sku_data ?? '';
      var skuJson = json.decode(qt_sku_data)as Map<String, dynamic>;
      print('完整的JSON数据：$jsonDecode  $qt_sku_data');
      var rank = skuJson['rank'] as int? ?? 0;
      print('rank:${rank};ok ${qt_list_data}');
      var exp_id = json.decode(qt_list_data)['exp_id']; // 修改这里
      print('jsonDecode:${skuJson['rank']},$qt_sku_data,$exp_id');
      QTCommonSdk.onEvent("page_list_product_exposure", {
        'spm_cnt': '$<EMAIL>@${_mapFilterOptionToSequence(currentFilterOption)}_prod@$rank.$spm',
        'scm_cnt': 'recommend.$exp_id.all_0.prod-${item.skuId}.${requestId}',
        'product_id': item.skuId,
        'product_name': item.skuName,
        'qt_list_data': qt_list_data,
        'qt_sku_data':item.qt_sku_data,
      });
      print('✅ 商品曝光埋点上报成功：skuId=${item.skuId}, 名称=${item.skuName}');
    } catch (e) {
      print('❌ 商品曝光埋点上报失败：skuId=${item.skuId}, 错误=$e');
    }
  }


  @override
  bool needKeepAlive() => true;

  @override
  bool isSubPage() => true;

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) => null;

  @override
  String getTitleName() {
    return "推荐品";
  }

  /// 将过滤选项映射为序号
  int _mapFilterOptionToSequence(dynamic option) {
    switch (option) {
      case -1:
        return 1; // 全部 -> 序号1
      case 1:
        return 2; // 优选品 -> 序号2
      case 2:
        return 3; // 控销品 -> 序号3
      case 3:
        return 4; // 甄选品 -> 序号4
      default:
        return 1; // 默认返回全部
    }
  }

  /// 根据选项获取标题
  String _getFilterTitleByOption(dynamic option) {
    switch (option) {
      case -1:
        return "全部";
      case 1:
        return "优选品";
      case 2:
        return "控销品";
      case 3:
        return "甄选品";
      default:
        return "全部";
    }
  }

}
