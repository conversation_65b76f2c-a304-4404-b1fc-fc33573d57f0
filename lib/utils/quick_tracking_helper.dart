import 'package:qt_common_sdk/qt_common_sdk.dart';

/// Quick Tracking SDK ������
/// �ṩ���õ�ͳ�ƹ��ܷ�װ
class QuickTrackingHelper {
  
  /// ҳ�濪ʼͳ��
  static void onPageStart(String pageName) {
    try {
      QTCommonSdk.onPageStart(pageName);
      print('Quick Tracking: ҳ�濪ʼͳ�� - $pageName');
    } catch (e) {
      print('Quick Tracking: ҳ�濪ʼͳ��ʧ�� - $pageName, ����: $e');
    }
  }

  /// ҳ�����ͳ��
  static void onPageEnd(String pageName) {
    try {
      QTCommonSdk.onPageEnd(pageName);
      print('Quick Tracking: ҳ�����ͳ�� - $pageName');
    } catch (e) {
      print('Quick Tracking: ҳ�����ͳ��ʧ�� - $pageName, ����: $e');
    }
  }

  /// �Զ����¼�ͳ��
  static void onEvent(String eventId, Map<String, dynamic> properties) {
    try {
      QTCommonSdk.onEvent(eventId, properties);
      print('Quick Tracking: �Զ����¼� - $eventId, ����: $properties');
    } catch (e) {
      print('Quick Tracking: �Զ����¼�ʧ�� - $eventId, ����: $e');
    }
  }

  /// ��ҳ�����Ƶ��Զ����¼�ͳ��
  static void onEventWithPage(String eventId, String pageName, Map<String, dynamic> properties) {
    try {
      QTCommonSdk.onEventWithPage(eventId, pageName, properties);
      print('Quick Tracking: ��ҳ���Զ����¼� - $eventId, ҳ��: $pageName, ����: $properties');
    } catch (e) {
      print('Quick Tracking: ��ҳ���Զ����¼�ʧ�� - $eventId, ����: $e');
    }
  }

  /// �û���¼ͳ��
  static void onProfileSignIn(String userId) {
    try {
      QTCommonSdk.onProfileSignIn(userId);
      print('Quick Tracking: �û���¼ͳ�� - $userId');
    } catch (e) {
      print('Quick Tracking: �û���¼ͳ��ʧ�� - $userId, ����: $e');
    }
  }

  /// �û��ǳ�ͳ��
  static void onProfileSignOff() {
    try {
      QTCommonSdk.onProfileSignOff();
      print('Quick Tracking: �û��ǳ�ͳ��');
    } catch (e) {
      print('Quick Tracking: �û��ǳ�ͳ��ʧ�� - $e');
    }
  }

  /// ����ҳ������
  static void setPageProperty(String pageName, Map<String, dynamic> properties) {
    try {
      QTCommonSdk.setPageProperty(pageName, properties);
      print('Quick Tracking: ����ҳ������ - $pageName, ����: $properties');
    } catch (e) {
      print('Quick Tracking: ����ҳ������ʧ�� - $pageName, ����: $e');
    }
  }

  /// ע��ȫ������
  static void registerGlobalProperties(Map<String, dynamic> properties) {
    try {
      QTCommonSdk.registerGlobalProperties(properties);
      print('Quick Tracking: ע��ȫ������ - $properties');
    } catch (e) {
      print('Quick Tracking: ע��ȫ������ʧ�� - $e');
    }
  }

  /// ɾ��ȫ������
  static void unregisterGlobalProperty(String propertyName) {
    try {
      QTCommonSdk.unregisterGlobalProperty(propertyName);
      print('Quick Tracking: ɾ��ȫ������ - $propertyName');
    } catch (e) {
      print('Quick Tracking: ɾ��ȫ������ʧ�� - $propertyName, ����: $e');
    }
  }

  /// ���ȫ������
  static void clearGlobalProperties() {
    try {
      QTCommonSdk.clearGlobalProperties();
      print('Quick Tracking: ���ȫ������');
    } catch (e) {
      print('Quick Tracking: ���ȫ������ʧ�� - $e');
    }
  }

  /// ����ҳ��ͳ��
  static void skipMe(String pageName) {
    try {
      QTCommonSdk.skipMe(pageName);
      print('Quick Tracking: ����ҳ��ͳ�� - $pageName');
    } catch (e) {
      print('Quick Tracking: ����ҳ��ͳ��ʧ�� - $pageName, ����: $e');
    }
  }

  /// �����Զ�������
  static void setCustomDomain(String primaryDomain, String standbyDomain) {
    try {
      QTCommonSdk.setCustomDomain(primaryDomain, standbyDomain);
      print('Quick Tracking: �����Զ������� - ������: $primaryDomain, ��������: $standbyDomain');
    } catch (e) {
      print('Quick Tracking: �����Զ�������ʧ�� - $e');
    }
  }

  /// ����Ӧ�ð汾��
  static void setAppVersion(String appVersion, int appVersionCode) {
    try {
      QTCommonSdk.setAppVersion(appVersion, appVersionCode);
      print('Quick Tracking: ����Ӧ�ð汾�� - �汾: $appVersion, �汾��: $appVersionCode');
    } catch (e) {
      print('Quick Tracking: ����Ӧ�ð汾��ʧ�� - $e');
    }
  }

  /// ������־����
  static void setLogEnabled(bool enabled) {
    try {
      QTCommonSdk.setLogEnabled(enabled);
      print('Quick Tracking: ������־���� - $enabled');
    } catch (e) {
      print('Quick Tracking: ������־����ʧ�� - $e');
    }
  }

  /// ��ȡȫ������
  static Future<String> getGlobalProperties() async {
    try {
      String properties = await QTCommonSdk.getGlobalProperties;
      print('Quick Tracking: ��ȡȫ������ - $properties');
      return properties;
    } catch (e) {
      print('Quick Tracking: ��ȡȫ������ʧ�� - $e');
      return '';
    }
  }

  /// ��ȡָ��ȫ������
  static Future<dynamic> getGlobalProperty(String propertyName) async {
    try {
      dynamic value = await QTCommonSdk.getGlobalProperty(propertyName);
      print('Quick Tracking: ��ȡȫ������ - $propertyName: $value');
      return value;
    } catch (e) {
      print('Quick Tracking: ��ȡȫ������ʧ�� - $propertyName, ����: $e');
      return null;
    }
  }

  /// ����JS���ã�����WebView�Žӣ�
  static void onJSCall(String msg) {
    try {
      QTCommonSdk.onJSCall(msg);
      print('Quick Tracking: JS���ô��� - $msg');
    } catch (e) {
      print('Quick Tracking: JS���ô���ʧ�� - $msg, ����: $e');
    }
  }

  /// Android�˽�����ɱǰ����
  static void onKillProcess() {
    try {
      QTCommonSdk.onKillProcess();
      print('Quick Tracking: ������ɱ����');
    } catch (e) {
      print('Quick Tracking: ������ɱ����ʧ�� - $e');
    }
  }
}
